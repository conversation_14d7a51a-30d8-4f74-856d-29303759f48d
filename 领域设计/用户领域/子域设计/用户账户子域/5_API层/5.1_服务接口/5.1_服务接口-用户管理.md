# 用户账户子域服务接口 - 用户管理

## 文档信息
- **版本**：2.0
- **作者**：系统设计团队
- **日期**：2025-06-20
- **状态**：更新中

## 1. 目的

本文档描述用户账户子域中用户管理相关的服务接口，包括用户的注册、登录、信息管理、状态管理等操作。

## 1.1 接口迁移说明

为了简化登录流程并提高代码复用性，我们将多个登录相关的接口整合到了一个统一登录接口中。以下接口已被标记为已弃用，并将在未来版本中移除：

1. **验证手机验证码并登录/注册**：请使用统一登录接口，并将`loginType`设置为`SMS_CODE`
2. **验证邮箱验证码并登录/注册**：请使用统一登录接口，并将`loginType`设置为`EMAIL_CODE`
3. **账号密码登录**：请使用统一登录接口，并将`loginType`设置为`PASSWORD`
4. **创建新账号（邮箱+手机号）**：请使用统一登录接口，并按照多步骤登录流程进行邮箱验证和手机号绑定

新的统一登录接口支持多种登录方式和多步骤登录流程，可以替代上述所有接口的功能。

## 2. 接口列表

### 2.1 统一登录接口

- **接口路径**：`POST /api/v1/login/register/unified`
- **功能描述**：支持多种登录方式和多步骤登录流程的统一接口
- **请求参数**：
  - 请求体：`UnifiedLoginRequest`
    - `loginType`：登录类型，枚举，必填，可选值为：
      - `SMS_CODE(0)`：短信验证码登录
      - `EMAIL_CODE(1)`：邮箱验证码登录
      - `PASSWORD(2)`：账号密码登录
      - `THIRD_PARTY(3)`：第三方登录
      - `UNIFIED(4)`：统一登录流程
    - `flowStatus`：流程状态，枚举，可选，可选值为：
      - `INIT`：初始状态
      - `EMAIL_VERIFIED`：邮箱验证完成
      - `THIRD_PARTY_AUTHORIZED`：第三方授权完成
      - `PHONE_EXISTS`：手机号已存在
      - `CONTINUE_VERIFY`：继续验证
      - `COMPLETED`：完成
    - `phone`：手机号，字符串，根据登录类型必填
    - `email`：邮箱，字符串，根据登录类型必填
    - `code`：验证码，字符串，根据登录类型必填
    - `password`：密码，字符串，根据登录类型必填
    - `platform`：平台类型，枚举，必填
    - `deviceInfo`：设备信息，对象，可选
    - `temporaryToken`：临时令牌，字符串，根据流程状态必填
    - `accountUseFlag`：是否沿用已有账号，布尔值，根据流程状态必填
    - `businessType`：业务类型，枚举，可选，默认为`REGISTER_LOGIN_CODE(0)`
    - `userAgent`：用户代理字符串，字符串，可选
- **响应结果**：
  - 成功：`200 OK`，返回`UnifiedLoginResponse`
    - `loginResponse`：登录响应，对象，登录成功时返回
    - `flowStatus`：流程状态，枚举，必返回
    - `needContinue`：是否需要继续流程，布尔值，必返回
    - `nextStepHint`：下一步提示，字符串，可选
    - `temporaryToken`：临时令牌，字符串，当需要继续流程时返回
    - `phoneExistsResponse`：手机号已存在响应，对象，当手机号已存在时返回
  - 失败：`400 Bad Request`，`500 Internal Server Error`
- **权限要求**：无需认证
- **请求示例**：
```json
{
  "loginType": "SMS_CODE",
  "phone": "**********",
  "code": "123456",
  "platform": "C_END",
  "deviceInfo": {
    "deviceId": "device-uuid-123",
    "deviceType": "MOBILE",
    "osType": "IOS",
    "osVersion": "15.0",
    "appVersion": "1.0.0"
  },
  "businessType": "REGISTER_LOGIN_CODE"
}
```
- **响应示例**：
```json
{
  "loginResponse": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "userInfo": {
      "userId": "123456789",
      "phone": "**********",
      "email": "<EMAIL>",
      "nickname": "User123",
      "avatar": "https://example.com/avatar.jpg"
    }
  },
  "flowStatus": "COMPLETED",
  "needContinue": false
}
```

### 2.2 发送手机验证码

- **接口路径**：`POST /api/v1/register/sendSmsCode`
- **功能描述**：发送手机验证码，用于注册或登录
- **请求参数**：
  - 请求体：`RegisterDTO.SendSmsCodeRequest`
    - `phone`：手机号，字符串，必填，格式为台湾手机号（例如：+************）
    - `businessType`：短信业务类型，枚举，必填，可选值为：
      - `REGISTER_LOGIN_CODE(0)`：注册/登录
      - `PHONE_UNBIND_CODE(1)`：换绑
      - `MERCHANT_CODE(2)`：商户
- **响应结果**：
  - 成功：`200 OK`，返回`RegisterDTO.SendCodeResponse`
  - 失败：`400 Bad Request`，`500 Internal Server Error`
- **权限要求**：无需认证
- **请求示例**：

```http
POST /api/v1/register/sendSmsCode HTTP/1.1
Content-Type: application/json

{
  "phone": "+************",
  "businessType": "REGISTER_LOGIN_CODE"
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "expireTime": 1620000300000
  },
  "timestamp": 1620000000000
}
```
- **接口定义**：

```java
@RestController
@RequestMapping("/api/v1")
public class LoginController {

    @PostMapping("/register/sendSmsCode")
    @Operation(summary = "发送手机验证码", description = "向指定的台湾手机号发送6位数字验证码，用于注册或登录验证")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "验证码发送成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误，如手机号格式不正确或发送频率受限"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<RegisterDTO.SendCodeResponse> sendSmsCode(
            @Valid @RequestBody RegisterDTO.SendSmsCodeRequest request) {
        // 创建命令对象
        SendSmsCodeCommand command = SendSmsCodeCommand.builder()
                .phone(request.getPhone())
                .businessType(request.getBusinessType())
                .build();

        // 调用应用服务
        Long expireTime = userApplicationService.sendSmsCode(command);

        // 构建响应
        RegisterDTO.SendCodeResponse response = new RegisterDTO.SendCodeResponse();
        response.setExpireTime(expireTime);
        return Result.success(response);
    }
}
```

### 2.3 发送邮箱验证码

- **接口路径**：`POST /api/v1/register/sendEmailCode`
- **功能描述**：发送邮箱验证码，用于注册或登录
- **请求参数**：
  - 请求体：`RegisterDTO.SendEmailCodeRequest`
    - `email`：邮箱地址，字符串，必填，格式为有效的邮箱地址（例如：<EMAIL>）
    - `businessType`：邮箱业务类型，枚举，必填，可选值为：
      - `REGISTER_LOGIN_CODE(0)`：注册/登录
      - `EMAIL_UNBIND_CODE(1)`：换绑
      - `MERCHANT_CODE(2)`：商户
- **响应结果**：
  - 成功：`200 OK`，返回`RegisterDTO.SendCodeResponse`
    - `expireTime`：验证码过期时间，毫秒时间戳
  - 失败：`400 Bad Request`，`500 Internal Server Error`
- **权限要求**：无需认证
- **请求示例**：

```http
POST /api/v1/register/sendEmailCode HTTP/1.1
Content-Type: application/json

{
  "email": "<EMAIL>",
  "businessType": "REGISTER_LOGIN_CODE"
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "userId": 123456789,
    "userInfo": {
      "username": "user123",
      "nickname": "用户昵称",
      "phone": "+************",
      "email": "<EMAIL>",
      "avatar": "https://example.com/avatar.jpg"
    },
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 3600,
    "needBindPhone": false
  },
  "timestamp": 1620000000000
}
```

- **响应示例**（新注册用户）：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "userId": 123456790,
    "userInfo": {
      "username": "user_123456790",
      "nickname": "用户123456790",
      "phone": "+************",
      "email": null,
      "avatar": "https://example.com/default-avatar.jpg"
    },
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenType": "Bearer",
    "expiresIn": 3600,
    "needBindPhone": false
  },
  "timestamp": 1620000000000
}
```
- **接口定义**：

```java
@RestController
@RequestMapping("/api/v1")
public class LoginController {

    @PostMapping("/register/verifySmsCodeAndLogin")
    @Operation(summary = "验证手机验证码,手机号/绑定手机号的登录/注册",
            description = "验证发送到台湾手机号的验证码。\n" +
                    "- 邮箱未注册绑定手机号\n" +
                    "- 验证失败时返回400状态码和错误信息\n" +
                    "- 手机号验证成功且用户已注册时，直接登录并返回登录信息\n" +
                    "- 手机号验证成功但用户未注册时，自动注册并登录返回登录信息\n\n" +
                    "- 邮箱+手机号验证成功且手机号未注册时，创建新账号并返回登录信息" +
                    "- 邮箱+手机号验证成功但手机号已注册时，返回手机号已存在响应" +
                    "可能的错误情况：\n" +
                    "- 验证码不正确：返回400状态码和错误信息\n" +
                    "- 验证码已过期：返回400状态码和错误信息\n" +
                    "- 手机号格式不正确：返回400状态码和错误信息\n" +
                    "- 服务器内部错误：返回500状态码和错误信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "验证成功"),
        @ApiResponse(responseCode = "400", description = "验证失败或请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<Object> verifySmsCodeAndLogin(
            @Valid @RequestBody RegisterDTO.VerifySmsCodeAndBindPhoneRequest request,
            HttpServletRequest httpRequest) {
        // 获取客户端IP
        String clientIp = clientIpService.getClientIp(httpRequest);

        // 创建命令对象
        VerifySmsCodeCommand command = VerifySmsCodeCommand.builder()
                .phone(request.getPhone())
                .code(request.getCode())
                .source(UserSource.CUSTOMER_LOGIN)
                .registrationType(RegistrationType.PHONE_REGISTRATION)
                .businessType(request.getBusinessType())
                .clientIp(clientIp)
                .build();

        // 调用应用服务
        return Result.success(userApplicationService.verifySmsCodeAndLogin(command));
    }
}
```

### 2.4 发送邮箱验证码

- **接口路径**：`POST /api/v1/register/sendEmailCode`
- **功能描述**：发送邮箱验证码，用于注册或登录
- **请求参数**：
  - 请求体：`RegisterDTO.SendEmailCodeRequest`
    - `email`：邮箱地址，字符串，必填，格式为有效的邮箱地址（例如：<EMAIL>）
    - `businessType`：邮箱业务类型，枚举，必填，可选值为：
      - `REGISTER_LOGIN_CODE(0)`：注册/登录
      - `PHONE_UNBIND_CODE(1)`：换绑
      - `MERCHANT_CODE(2)`：商户
- **响应结果**：
  - 成功：`200 OK`，返回`RegisterDTO.SendCodeResponse`
  - 失败：`400 Bad Request`，`500 Internal Server Error`
- **权限要求**：无需认证
- **请求示例**：

```http
POST /api/v1/register/sendEmailCode HTTP/1.1
Content-Type: application/json

{
  "email": "<EMAIL>",
  "businessType": "REGISTER_LOGIN_CODE"
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "expireTime": 1620000300000
  },
  "timestamp": 1620000000000
}
```
- **接口定义**：

```java
@RestController
@RequestMapping("/api/v1")
public class LoginController {

    @PostMapping("/register/sendEmailCode")
    @Operation(summary = "发送邮箱验证码", description = "向指定的邮箱发送6位数字验证码，用于注册或登录验证")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "验证码发送成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误，如邮箱格式不正确或发送频率受限"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<RegisterDTO.SendCodeResponse> sendEmailCode(
            @Valid @RequestBody RegisterDTO.SendEmailCodeRequest request) {
        // 创建命令对象
        SendEmailCodeCommand command = SendEmailCodeCommand.builder()
                .email(request.getEmail())
                .businessType(request.getBusinessType())
                .build();

        // 调用应用服务
        Long expireTime = userApplicationService.sendEmailCode(command);

        // 构建响应
        RegisterDTO.SendCodeResponse response = new RegisterDTO.SendCodeResponse();
        response.setExpireTime(expireTime);
        return Result.success(response);
    }
}
```

### 2.4 验证手机验证码

- **接口路径**：`POST /api/v1/verify/smsCode`
- **功能描述**：验证手机验证码，仅验证不登录
- **请求参数**：
  - 请求体：`RegisterDTO.VerifySmsCodeAndBindPhoneRequest`
    - `phone`：手机号，字符串，必填，格式为台湾手机号（例如：+************）
    - `code`：验证码，字符串，必填，6位数字
    - `businessType`：短信业务类型，枚举，必填，可选值为：
      - `REGISTER_LOGIN_CODE(0)`：注册/登录
      - `PHONE_UNBIND_CODE(1)`：换绑
      - `MERCHANT_CODE(2)`：商户
- **响应结果**：
  - 成功：`200 OK`
  - 失败：`400 Bad Request`，`500 Internal Server Error`
- **权限要求**：无需认证
- **请求示例**：

```http
POST /api/v1/verify/smsCode HTTP/1.1
Content-Type: application/json

{
  "phone": "+************",
  "code": "123456",
  "businessType": "REGISTER_LOGIN_CODE"
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null,
  "timestamp": 1620000000000
}
```
- **接口定义**：

```java
@RestController
@RequestMapping("/api/v1")
public class LoginController {

    @PostMapping("/verify/smsCode")
    @Operation(summary = "验证手机验证码")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "验证成功"),
        @ApiResponse(responseCode = "400", description = "验证失败或请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result verifySmsCode(
            @Valid @RequestBody RegisterDTO.VerifySmsCodeAndBindPhoneRequest request) {
        try {
            verificationService.verifySmsCode(request.getPhone(), request.getCode(), request.getBusinessType());
            return Result.success();
        } catch (Exception e) {
            return Result.error(UsersErrorCode.SMS_CODE_CHECK_ERROR.getCode(), UsersErrorCode.GENDER_CHECK_ERROR.getMsg());
        }
    }
}
```

### 2.7 验证邮箱验证码

- **接口路径**：`POST /api/v1/verify/emailCode`
- **功能描述**：验证邮箱验证码，仅验证不登录
- **请求参数**：
  - 请求体：`RegisterDTO.VerifyEmailCodeRequest`
    - `email`：邮箱地址，字符串，必填，格式为有效的邮箱地址（例如：<EMAIL>）
    - `code`：验证码，字符串，必填，6位数字
    - `businessType`：邮箱业务类型，枚举，必填，可选值为：
      - `REGISTER_LOGIN_CODE(0)`：注册/登录
      - `PHONE_UNBIND_CODE(1)`：换绑
      - `MERCHANT_CODE(2)`：商户
- **响应结果**：
  - 成功：`200 OK`
  - 失败：`400 Bad Request`，`500 Internal Server Error`
- **权限要求**：无需认证
- **请求示例**：

```http
POST /api/v1/verify/emailCode HTTP/1.1
Content-Type: application/json

{
  "email": "<EMAIL>",
  "code": "123456",
  "businessType": "REGISTER_LOGIN_CODE"
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null,
  "timestamp": 1620000000000
}
```
- **接口定义**：

```java
@RestController
@RequestMapping("/api/v1")
public class LoginController {

    @PostMapping("/verify/emailCode")
    @Operation(summary = "验证邮箱验证码", description = "向指定的邮箱发送验证码并验证，用于注册或登录验证")
    public Result verifyEmailCode(
            @Parameter(description = "包含邮箱的请求对象", required = true)
            @Valid @RequestBody VerifyEmailCodeRequest request) {
        try {
            verificationService.verifyEmailCode(request.getEmail(), request.getCode(), request.getBusinessType());
            return Result.success();
        } catch (Exception e) {
            return Result.error(UsersErrorCode.EMAIL_CODE_CHECK_ERROR.getCode(), UsersErrorCode.GENDER_CHECK_ERROR.getMsg());

        }
    }
}
```

### 2.5 验证邮箱验证码

- **接口路径**：`POST /api/v1/verify/emailCode`
- **功能描述**：验证邮箱验证码，仅验证不登录
- **请求参数**：
  - 请求体：`VerifyDTO.VerifyEmailCodeRequest`
    - `email`：邮箱地址，字符串，必填，格式为有效的邮箱地址（例如：<EMAIL>）
    - `code`：验证码，字符串，必填，6位数字
    - `businessType`：邮箱业务类型，枚举，必填，可选值为：
      - `REGISTER_LOGIN_CODE(0)`：注册/登录
      - `EMAIL_UNBIND_CODE(1)`：换绑
      - `MERCHANT_CODE(2)`：商户
- **响应结果**：
  - 成功：`200 OK`，返回`VerifyDTO.VerifyCodeResponse`
    - `verified`：是否验证成功，布尔值
  - 失败：`400 Bad Request`，`500 Internal Server Error`
- **权限要求**：无需认证
- **请求示例**：

```http
POST /api/v1/verify/emailCode HTTP/1.1
Content-Type: application/json

{
  "email": "<EMAIL>",
  "code": "123456",
  "businessType": "REGISTER_LOGIN_CODE"
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "verified": true
  },
  "timestamp": 1620000000000
}
```

### 2.6 三方登录

- **接口路径**：`GET /api/v1/login/{type}`
- **功能描述**：重定向到第三方平台授权页面
- **请求参数**：
  - 路径参数：
    - `type`：第三方平台类型，如`google`、`line`等
- **响应结果**：
  - 成功：重定向到第三方平台授权页面
  - 失败：返回错误信息
- **权限要求**：无需认证
- **请求示例**：

```http
GET /api/v1/login/google HTTP/1.1
Host: api.example.com
```

- **接口定义**：

```java
@RestController
@RequestMapping("/api/v1")
public class LoginController {

    @Operation(summary = "三方登录")
    @GetMapping("/login/{type}")
    public void login(@PathVariable String type, HttpServletResponse response) {
        try {
            log.info("login type:{}", type);
            String callbackUrl = userPersonService.login(type);
            log.info("callbackUrl:{}", callbackUrl);
            response.sendRedirect(callbackUrl);
        } catch (Exception e) {
            writeJsonError(response, UsersErrorCode.USER_AUTHORIZATION_ERROR.getCode(),
                    UsersErrorCode.USER_AUTHORIZATION_ERROR.getMsg());
        }
    }
}
```

### 2.7 三方登录回调

- **接口路径**：`GET /{type}/{platform}/{deviceId}/callback`
- **功能描述**：处理第三方平台的授权回调
- **请求参数**：
  - 路径参数：
    - `type`：第三方平台类型，如`google`、`line`等
    - `platform`：平台类型，如`ios`、`android`等
    - `deviceId`：设备ID
  - 查询参数：
    - `code`：授权码，由第三方平台返回
    - `state`：状态参数，用于防止CSRF攻击
- **响应结果**：
  - 成功：重定向到应用内部页面
  - 失败：返回错误信息
- **权限要求**：无需认证
- **请求示例**：

```http
GET /google/ios/device123/callback?code=4/0AY0e-g6VC7TiDjh&state=xyz HTTP/1.1
Host: api.example.com
```

- **接口定义**：

```java
@RestController
@RequestMapping("/api/v1")
public class LoginController {

    @Operation(summary = "三方登录回调")
    @RequestMapping("/{type}/{platform}/{deviceId}/callback")
    public void loginCallback(@PathVariable String type, @PathVariable String platform,
                             @PathVariable String deviceId, AuthCallbackRequest callback,
                             HttpServletResponse response) throws IOException {
        try {
            log.info("loginCallback type:{},platform:{},deviceId:{}", type, platform, deviceId);
            String redirectUrl = userPersonService.loginCallback(type, platform, deviceId, callback);
            log.info("redirectUrl:{}", redirectUrl);
            response.sendRedirect(redirectUrl);
        } catch (Exception e) {
            writeJsonError(response, UsersErrorCode.USER_AUTHORIZATION_ERROR.getCode(),
                    UsersErrorCode.USER_AUTHORIZATION_ERROR.getMsg());
        }
    }
}
```

### 2.8 退出登录

- **接口路径**：`POST /api/v1/logout`
- **功能描述**：退出当前用户的登录状态
- **请求参数**：无
- **响应结果**：
  - 成功：`200 OK`
  - 失败：
    - `400 Bad Request`：请求参数错误
    - `401 Unauthorized`：未登录或登录已过期
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要用户登录
- **请求示例**：

```http
POST /api/v1/logout HTTP/1.1
Host: api.example.com
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null,
  "timestamp": 1620000000000
}
```

### 2.9 注销用户

- **接口路径**：`POST /api/v1/deleteUser`
- **功能描述**：注销当前用户账号
- **请求参数**：无
- **响应结果**：
  - 成功：`200 OK`
  - 失败：
    - `400 Bad Request`：请求参数错误
    - `401 Unauthorized`：未登录或登录已过期
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要用户登录
- **请求示例**：

```http
POST /api/v1/deleteUser HTTP/1.1
Host: api.example.com
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null,
  "timestamp": 1620000000000
}
```

## 4. 错误码

| 错误码 | 错误消息 | 描述 |
|-------|---------|------|
| USER_NOT_FOUND | 用户不存在 | 指定的用户ID不存在 |
| USERNAME_EXISTS | 用户名已存在 | 注册时使用的用户名已被占用 |
| PHONE_EXISTS | 手机号已存在 | 注册时使用的手机号已被占用 |
| EMAIL_EXISTS | 邮箱已存在 | 注册时使用的邮箱已被占用 |
| INVALID_CREDENTIAL | 无效的登录凭证 | 登录时提供的凭证无效 |
| INVALID_PASSWORD | 密码错误 | 登录或修改密码时提供的密码错误 |
| ACCOUNT_LOCKED | 账户已锁定 | 用户账户已被锁定 |
| ACCOUNT_FROZEN | 账户已冻结 | 用户账户已被冻结 |
| ACCOUNT_DEREGISTERED | 账户已注销 | 用户账户已被注销 |
| INVALID_VERIFICATION_CODE | 验证码无效 | 提供的验证码无效或已过期 |
| INVALID_STATUS_TRANSITION | 无效的状态转换 | 请求的状态转换不允许 |
| TOO_MANY_ATTEMPTS | 尝试次数过多 | 登录尝试次数过多，账户已被临时锁定 |

### 2.10 三方登录

- **接口路径**：`GET /api/v1/login/{type}`
- **功能描述**：重定向到第三方平台授权页面
- **请求参数**：
  - 路径参数：
    - `type`：第三方平台类型，如`google`、`line`等
- **响应结果**：
  - 成功：重定向到第三方平台授权页面
  - 失败：返回错误信息
- **权限要求**：无需认证
- **请求示例**：

```http
GET /api/v1/login/google HTTP/1.1
Host: api.example.com
```

- **接口定义**：

```java
@RestController
@RequestMapping("/api/v1")
public class LoginController {

    @Operation(summary = "三方登录")
    @GetMapping("/login/{type}")
    public void login(@PathVariable String type, HttpServletResponse response) {
        try {
            log.info("login type:{}", type);
            String callbackUrl = userPersonService.login(type);
            log.info("callbackUrl:{}", callbackUrl);
            response.sendRedirect(callbackUrl);
        } catch (Exception e) {
            writeJsonError(response, UsersErrorCode.USER_AUTHORIZATION_ERROR.getCode(),
                    UsersErrorCode.USER_AUTHORIZATION_ERROR.getMsg());
        }
    }
}
```

### 2.11 三方登录回调

- **接口路径**：`GET /{type}/{platform}/{deviceId}/callback`
- **功能描述**：处理第三方平台的授权回调
- **请求参数**：
  - 路径参数：
    - `type`：第三方平台类型，如`google`、`line`等
    - `platform`：平台类型，如`ios`、`android`等
    - `deviceId`：设备ID
  - 查询参数：
    - `code`：授权码，由第三方平台返回
    - `state`：状态参数，用于防止CSRF攻击
- **响应结果**：
  - 成功：重定向到应用内部页面
  - 失败：返回错误信息
- **权限要求**：无需认证
- **请求示例**：

```http
GET /google/ios/device123/callback?code=4/0AY0e-g6VC7TiDjh&state=xyz HTTP/1.1
Host: api.example.com
```

- **接口定义**：

```java
@RestController
@RequestMapping("/api/v1")
public class LoginController {

    @Operation(summary = "三方登录回调")
    @RequestMapping("/{type}/{platform}/{deviceId}/callback")
    public void loginCallback(@PathVariable String type, @PathVariable String platform,
                             @PathVariable String deviceId, AuthCallbackRequest callback,
                             HttpServletResponse response) throws IOException {
        try {
            log.info("loginCallback type:{},platform:{},deviceId:{}", type, platform, deviceId);
            String redirectUrl = userPersonService.loginCallback(type, platform, deviceId, callback);
            log.info("redirectUrl:{}", redirectUrl);
            response.sendRedirect(redirectUrl);
        } catch (Exception e) {
            writeJsonError(response, UsersErrorCode.USER_AUTHORIZATION_ERROR.getCode(),
                    UsersErrorCode.USER_AUTHORIZATION_ERROR.getMsg());
        }
    }
}
```

### 2.12 退出登录

- **接口路径**：`POST /api/v1/logout`
- **功能描述**：退出当前用户的登录状态
- **请求参数**：无
- **响应结果**：
  - 成功：`200 OK`
  - 失败：
    - `400 Bad Request`：请求参数错误
    - `401 Unauthorized`：未登录或登录已过期
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要用户登录
- **请求示例**：

```http
POST /api/v1/logout HTTP/1.1
Host: api.example.com
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null,
  "timestamp": 1620000000000
}
```

- **接口定义**：

```java
@RestController
@RequestMapping("/api/v1")
public class LoginController {

    @Operation(summary = "退出登录")
    @PostMapping("/logout")
    public Result logout() {
        try {
            userPersonService.logout();
        } catch (BusinessException e) {
            return Result.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            return Result.error(UsersErrorCode.USER_LOGOUT_ERROR.getCode(),
                    UsersErrorCode.USER_LOGOUT_ERROR.getMsg());
        }
        return Result.success();
    }
}
```

### 2.13 注销用户

- **接口路径**：`POST /api/v1/deleteUser`
- **功能描述**：注销当前用户账号
- **请求参数**：无
- **响应结果**：
  - 成功：`200 OK`
  - 失败：
    - `400 Bad Request`：请求参数错误
    - `401 Unauthorized`：未登录或登录已过期
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要用户登录
- **请求示例**：

```http
POST /api/v1/deleteUser HTTP/1.1
Host: api.example.com
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null,
  "timestamp": 1620000000000
}
```

- **接口定义**：

```java
@RestController
@RequestMapping("/api/v1")
public class LoginController {

    @Operation(summary = "注销用户")
    @PostMapping("/deleteUser")
    public Result deleteUser() {
        try {
            userPersonService.deletedUser();
        } catch (BusinessException e) {
            return Result.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            return Result.error(UsersErrorCode.USER_CANCEL_ERROR.getCode(),
                    UsersErrorCode.USER_CANCEL_ERROR.getMsg());
        }
        return Result.success();
    }
}
```

## 变更历史
| 版本 | 日期 | 作者 | 变更描述 | 关联功能/需求 |
|-----|------|------|---------|-------------|
| 1.0 | 2025-04-27 | 系统设计团队 | 初始版本 | - |
| 1.1 | 2025-06-15 | 系统设计团队 | 增加 SmsAndEmailBusinessType 业务类型枚举参数，添加验证手机验证码和验证邮箱验证码接口 | 用户验证码业务类型区分 |
| 1.2 | 2025-05-20 | 系统设计团队 | 添加账号密码登录接口 | 用户账号密码登录 |
| 1.3 | 2025-05-21 | 系统设计团队 | 添加三方登录、三方登录回调、退出登录和注销用户接口 | 用户登录管理 |
| 2.0 | 2025-06-20 | 系统设计团队 | 添加统一登录接口，支持多种登录方式和多步骤登录流程 | 用户登录流程优化 |
