# 用户账户子域服务接口索引 - 商户登录

## 文档信息
- **版本**：1.0
- **作者**：系统设计团队
- **日期**：2025-05-20
- **状态**：草稿

## 1. 目的

本文档提供用户账户子域中商户登录服务接口的索引，帮助开发人员快速查找和导航相关接口文档。

## 2. 接口索引

| 序号 | 接口名称 | 接口路径 | 功能描述 | 文档位置 |
|------|---------|---------|---------|---------|
| 1 | 发送手机验证码 | `POST /merchant/api/v1/register/sendSmsCode` | 向指定的台湾手机号发送6位数字验证码，用于商户注册或登录验证 | [3.1 发送手机验证码](./5.1_服务接口-商户登录.md#31-发送手机验证码) |
| 2 | 发送邮箱验证码 | `POST /merchant/api/v1/register/sendEmailCode` | 向指定的邮箱发送6位数字验证码，用于商户注册或登录验证 | [3.2 发送邮箱验证码](./5.1_服务接口-商户登录.md#32-发送邮箱验证码) |
| 3 | 验证手机验证码并创建商户账户 | `POST /merchant/api/v1/register/verifySmsCodeAndCreateMerchant` | 验证发送到手机的验证码并创建商户账户 | [3.3 验证手机验证码并创建商户账户](./5.1_服务接口-商户登录.md#33-验证手机验证码并创建商户账户) |
| 4 | 验证邮箱验证码并登录 | `POST /merchant/api/v1/register/verifyEmailCodeAndLogin` | 验证发送到邮箱的验证码，验证成功且邮箱已注册时直接登录，验证成功但邮箱未注册时返回需要绑定手机号的状态 | [3.4 验证邮箱验证码并登录](./5.1_服务接口-商户登录.md#34-验证邮箱验证码并登录) |
| 5 | 验证邮箱验证码并创建商户账户 | `POST /merchant/api/v1/register/verifyEmailCodeAndCreateMerchant` | 验证发送到邮箱的验证码并创建商户账户 | [3.5 验证邮箱验证码并创建商户账户](./5.1_服务接口-商户登录.md#35-验证邮箱验证码并创建商户账户) |
| 6 | 创建新账号或使用已有账号（邮箱+手机号） | `POST /merchant/api/v1/register/createNewAccountWithEmail` | 根据accountUseFlag参数决定是使用已有账号还是创建新账号 | [3.6 创建新账号或使用已有账号（邮箱+手机号）](./5.1_服务接口-商户登录.md#36-创建新账号或使用已有账号邮箱手机号) |
| 7 | 退出登录 | `POST /merchant/api/v1/logout` | 退出当前用户的登录状态 | [3.7 退出登录](./5.1_服务接口-商户登录.md#37-退出登录) |
| 8 | 验证邮箱验证码 | `POST /merchant/api/v1/verify/emailCode` | 验证发送到邮箱的验证码是否有效 | [3.1 验证邮箱验证码](./5.1_服务接口-商户账户管理.md#31-验证邮箱验证码) |
| 9 | 验证手机验证码 | `POST /merchant/api/v1/verify/smsCode` | 验证发送到手机的验证码是否有效 | [3.2 验证手机验证码](./5.1_服务接口-商户账户管理.md#32-验证手机验证码) |
| 10 | 验证手机号 | `POST /merchant/api/v1/phone/verify` | 验证用户手机号 | [3.3 验证手机号](./5.1_服务接口-商户账户管理.md#33-验证手机号) |
| 11 | 换绑手机号 | `POST /merchant/api/v1/phone/update` | 更新用户绑定的手机号 | [3.4 换绑手机号](./5.1_服务接口-商户账户管理.md#34-换绑手机号) |
| 12 | 绑定邮箱 | `POST /merchant/api/v1/email/bind` | 绑定邮箱到用户账户 | [3.5 绑定邮箱](./5.1_服务接口-商户账户管理.md#35-绑定邮箱) |
| 13 | 验证原邮箱 | `POST /merchant/api/v1/email/verify` | 验证用户原邮箱 | [3.6 验证原邮箱](./5.1_服务接口-商户账户管理.md#36-验证原邮箱) |

## 3. 接口分类

### 3.1 验证码相关接口

- **发送手机验证码**：`POST /merchant/api/v1/register/sendSmsCode`
- **发送邮箱验证码**：`POST /merchant/api/v1/register/sendEmailCode`
- **验证邮箱验证码**：`POST /merchant/api/v1/verify/emailCode`
- **验证手机验证码**：`POST /merchant/api/v1/verify/smsCode`

### 3.2 登录注册接口

- **验证手机验证码并创建商户账户**：`POST /merchant/api/v1/register/verifySmsCodeAndCreateMerchant`
- **验证邮箱验证码并登录**：`POST /merchant/api/v1/register/verifyEmailCodeAndLogin`
- **验证邮箱验证码并创建商户账户**：`POST /merchant/api/v1/register/verifyEmailCodeAndCreateMerchant`
- **创建新账号或使用已有账号**：`POST /merchant/api/v1/register/createNewAccountWithEmail`

### 3.3 账户管理接口

- **退出登录**：`POST /merchant/api/v1/logout`
- **验证手机号**：`POST /merchant/api/v1/phone/verify`
- **换绑手机号**：`POST /merchant/api/v1/phone/update`
- **绑定邮箱**：`POST /merchant/api/v1/email/bind`
- **验证原邮箱**：`POST /merchant/api/v1/email/verify`

## 4. 枚举定义

### 4.1 短信业务类型枚举（SmsAndEmailBusinessType）

```java
/**
 * 短信业务类型枚举
 */
@Schema(description = "业务类型用来发送手机短信或者邮箱key区分")
public enum SmsAndEmailBusinessType {
    /**
     * 注册/登录
     */
    REGISTER_LOGIN_CODE(0, "REGISTER_LOGIN_CODE", "注册"),

    /**
     * 换绑
     */
    PHONE_UNBIND_CODE(1, "PHONE_UNBIND_CODE", "换绑"),

    /**
     * 商户
     */
    MERCHANT_CODE(2, "MERCHANT_CODE", "商户");

    // 属性和方法省略
}
```

**说明**：
- 在请求中传递枚举值时，使用code值（0、1、2）
- 默认情况下，商户相关接口使用`MERCHANT_CODE(2)`

## 5. 常用请求/响应模型

### 5.1 请求模型

- **SendSmsCodeRequest**：发送手机验证码请求
- **SendEmailCodeRequest**：发送邮箱验证码请求
- **VerifySmsCodeAndBindPhoneRequest**：验证手机验证码请求
- **VerifyEmailCodeAndLoginRequest**：验证邮箱验证码并登录请求
- **CreateNewAccountWithEmailRequest**：创建新账号请求
- **AccountDeviceDTO**：账号设备信息
- **VerifyEmailCodeRequest**：验证邮箱验证码请求
- **UserAccountUpdateDTO.PhoneVerifyRequest**：验证手机号请求
- **UserAccountUpdateDTO.PhoneUpdateRequest**：换绑手机号请求
- **UserAccountUpdateDTO.EmailBindRequest**：绑定邮箱请求
- **UserAccountUpdateDTO.EmailVerifyRequest**：验证原邮箱请求

### 5.2 响应模型

- **SendCodeResponse**：发送验证码响应
- **LoginResponse**：登录响应
- **ErrorResponse**：错误响应
- **Result**：通用响应结果

## 6. 相关文档

- [商户登录服务接口](./5.1_服务接口-商户登录.md)：包含商户登录、注册相关接口
- [商户账户管理服务接口](./5.1_服务接口-商户账户管理.md)：包含商户账户管理相关接口
- [DTO设计](../5.2_DTO设计/5.2_DTO设计.md)：包含请求和响应的数据传输对象设计

## 变更历史
| 版本 | 日期 | 作者 | 变更描述 | 关联功能/需求 |
|-----|------|------|---------|-------------|
| 1.0 | 2025-05-20 | 系统设计团队 | 初始版本 | 商户登录服务接口索引 |
| 1.1 | 2025-06-01 | 系统设计团队 | 添加验证码验证、手机号和邮箱管理接口 | 商户账户管理功能 |
