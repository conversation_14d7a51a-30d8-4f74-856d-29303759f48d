# 商户账户管理服务接口

## 文档信息
- **版本**：1.0
- **作者**：系统设计团队
- **日期**：2025-05-20
- **状态**：草稿

## 1. 概述

本文档详细描述商户账户管理相关的服务接口，包括验证邮箱验证码、验证手机验证码、验证手机号、换绑手机号、绑定邮箱、验证原邮箱等功能。

## 2. 接口列表

| 序号 | 接口名称 | 接口路径 | 功能描述 |
|------|---------|---------|---------|
| 1 | 验证邮箱验证码 | `POST /merchant/api/v1/verify/emailCode` | 验证发送到邮箱的验证码是否有效 |
| 2 | 验证手机验证码 | `POST /merchant/api/v1/verify/smsCode` | 验证发送到手机的验证码是否有效 |
| 3 | 验证手机号 | `POST /merchant/api/v1/phone/verify` | 验证用户手机号 |
| 4 | 换绑手机号 | `POST /merchant/api/v1/phone/update` | 更新用户绑定的手机号 |
| 5 | 绑定邮箱 | `POST /merchant/api/v1/email/bind` | 绑定邮箱到用户账户 |
| 6 | 验证原邮箱 | `POST /merchant/api/v1/email/verify` | 验证用户原邮箱 |

## 3. 接口详情

### 3.1 验证邮箱验证码

- **接口路径**：`POST /merchant/api/v1/verify/emailCode`
- **功能描述**：验证发送到邮箱的验证码是否有效
- **请求参数**：
  - 请求体：`VerifyEmailCodeRequest`
    - `email`：邮箱地址，字符串，必填，格式为有效的邮箱地址（例如：<EMAIL>）
    - `code`：6位数字验证码，字符串，必填
    - `businessType`：短信业务类型，枚举，必填，默认为`2`（MERCHANT_CODE）
- **响应结果**：
  - 成功：`200 OK`
  - 失败：
    - `400 Bad Request`：验证失败或请求参数错误
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：无需认证
- **请求示例**：

```http
POST /merchant/api/v1/verify/emailCode HTTP/1.1
Content-Type: application/json

{
  "email": "<EMAIL>",
  "code": "123456",
  "businessType": 2
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 3.2 验证手机验证码

- **接口路径**：`POST /merchant/api/v1/verify/smsCode`
- **功能描述**：验证发送到手机的验证码是否有效
- **请求参数**：
  - 请求体：`VerifySmsCodeAndBindPhoneRequest`
    - `phone`：手机号，字符串，必填，格式为台湾手机号（例如：**********）
    - `code`：6位数字验证码，字符串，必填
    - `businessType`：短信业务类型，枚举，必填，默认为`2`（MERCHANT_CODE）
- **响应结果**：
  - 成功：`200 OK`
  - 失败：
    - `400 Bad Request`：验证失败或请求参数错误
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：无需认证
- **请求示例**：

```http
POST /merchant/api/v1/verify/smsCode HTTP/1.1
Content-Type: application/json

{
  "phone": "**********",
  "code": "123456",
  "businessType": 2
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 3.3 验证手机号

- **接口路径**：`POST /merchant/api/v1/phone/verify`
- **功能描述**：验证用户手机号
- **请求参数**：
  - 请求体：`UserAccountUpdateDTO.PhoneVerifyRequest`
    - `phone`：手机号，字符串，必填，格式为台湾手机号（例如：**********）
- **响应结果**：
  - 成功：`200 OK`
  - 失败：
    - `400 Bad Request`：验证失败或请求参数错误
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要用户认证
- **请求示例**：

```http
POST /merchant/api/v1/phone/verify HTTP/1.1
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "phone": "**********"
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 3.4 换绑手机号

- **接口路径**：`POST /merchant/api/v1/phone/update`
- **功能描述**：更新用户绑定的手机号
- **请求参数**：
  - 请求体：`UserAccountUpdateDTO.PhoneUpdateRequest`
    - `newPhone`：新手机号，字符串，必填，格式为台湾手机号（例如：**********）
    - `code`：验证码，字符串，必填
    - `deviceId`：设备ID，字符串，可选
    - `businessType`：短信业务类型，枚举，必填，默认为`1`（PHONE_UNBIND_CODE）
    - `source`：平台类型，枚举，可选
- **响应结果**：
  - 成功：`200 OK`
  - 失败：
    - `400 Bad Request`：请求参数错误
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要用户认证
- **请求示例**：

```http
POST /merchant/api/v1/phone/update HTTP/1.1
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "newPhone": "**********",
  "code": "123456",
  "businessType": 1,
  "source": "MERCHANT"
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 3.5 绑定邮箱

- **接口路径**：`POST /merchant/api/v1/email/bind`
- **功能描述**：绑定邮箱到用户账户
- **请求参数**：
  - 请求体：`UserAccountUpdateDTO.EmailBindRequest`
    - `email`：邮箱，字符串，必填，格式为有效的邮箱地址（例如：<EMAIL>）
    - `code`：验证码，字符串，可选
    - `businessType`：短信业务类型，枚举，必填，默认为`1`（PHONE_UNBIND_CODE）
    - `source`：平台类型，枚举，可选
- **响应结果**：
  - 成功：`200 OK`
  - 失败：
    - `400 Bad Request`：请求参数错误
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要用户认证
- **请求示例**：

```http
POST /merchant/api/v1/email/bind HTTP/1.1
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "email": "<EMAIL>",
  "code": "123456",
  "businessType": 1,
  "source": "MERCHANT"
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 3.6 验证原邮箱

- **接口路径**：`POST /merchant/api/v1/email/verify`
- **功能描述**：验证用户原邮箱
- **请求参数**：
  - 请求体：`UserAccountUpdateDTO.EmailVerifyRequest`
    - `email`：原邮箱，字符串，必填，格式为有效的邮箱地址（例如：<EMAIL>）
- **响应结果**：
  - 成功：`200 OK`
  - 失败：
    - `400 Bad Request`：请求参数错误
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要用户认证
- **请求示例**：

```http
POST /merchant/api/v1/email/verify HTTP/1.1
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "email": "<EMAIL>"
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

## 4. 请求/响应模型

### 4.1 请求模型

#### 4.1.1 VerifyEmailCodeRequest

```java
@Data
@Schema(description = "验证邮箱验证码请求")
public class VerifyEmailCodeRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "邮箱不能为空")
    @Schema(description = "邮箱地址", example = "<EMAIL>", required = true)
    private String email;

    @NotBlank(message = "验证码不能为空")
    @Schema(description = "6位数字验证码", example = "123456", required = true)
    private String code;

    @NotNull
    @Schema(description = "短信业务类型：PHONE_REGISTER-手机号注册，PHONE_UNBIND-解绑手机号，MERCHANT_CODE-商户注册", example = "MERCHANT_CODE")
    private SmsAndEmailBusinessType businessType = SmsAndEmailBusinessType.MERCHANT_CODE;
}
```

#### 4.1.2 VerifySmsCodeAndBindPhoneRequest

```java
@Data
@Schema(description = "验证手机验证码请求")
public class VerifySmsCodeAndBindPhoneRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "手机号不能为空")
    @Schema(description = "台湾手机号，以09开头，长度10位", example = "**********", required = true)
    private String phone;

    @NotBlank(message = "验证码不能为空")
    @Schema(description = "6位数字验证码", example = "123456", required = true)
    private String code;

    @NotNull
    @Schema(description = "短信业务类型：PHONE_REGISTER-手机号注册，PHONE_UNBIND-解绑手机号，MERCHANT_CODE-商户注册", example = "MERCHANT_CODE")
    private SmsAndEmailBusinessType businessType = SmsAndEmailBusinessType.MERCHANT_CODE;
}
```

#### 4.1.3 UserAccountUpdateDTO.PhoneVerifyRequest

```java
@Data
@Schema(description = "手机号验证请求")
public static class PhoneVerifyRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^09\\d{8}$", message = "请输入正确的台湾手机号")
    @Schema(description = "手机号", example = "**********", required = true)
    private String phone;
}
```

#### 4.1.4 UserAccountUpdateDTO.PhoneUpdateRequest

```java
@Data
@Schema(description = "换绑手机号请求")
public static class PhoneUpdateRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "新手机号不能为空")
    @Pattern(regexp = "^09\\d{8}$", message = "请输入正确的台湾手机号")
    @Schema(description = "新手机号", example = "**********", required = true)
    private String newPhone;

    @NotBlank(message = "验证码不能为空")
    @Schema(description = "验证码", example = "123456", required = true)
    private String code;

    @Schema(description = "设备ID", example = "device-uuid-123")
    private String deviceId;

    @NotNull
    @Schema(description = "短信业务类型：PHONE_REGISTER-手机号注册，PHONE_UNBIND-解绑手机号，MERCHANT_REGISTER-商户注册", example = "1")
    private SmsAndEmailBusinessType businessType = SmsAndEmailBusinessType.PHONE_UNBIND_CODE;

    @Schema(description = "平台类型", example = "CUSTOMER")
    private PlatformType source;
}
```

#### 4.1.5 UserAccountUpdateDTO.EmailBindRequest

```java
@Data
@Schema(description = "绑定邮箱请求")
public static class EmailBindRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @Schema(description = "邮箱", example = "<EMAIL>", required = true)
    private String email;

    @Schema(description = "验证码", example = "123456")
    private String code;

    @NotNull
    @Schema(description = "短信业务类型：PHONE_REGISTER-手机号注册，PHONE_UNBIND-解绑手机号，MERCHANT_REGISTER-商户注册", example = "1")
    private SmsAndEmailBusinessType businessType = SmsAndEmailBusinessType.PHONE_UNBIND_CODE;

    @Schema(description = "平台类型", example = "CUSTOMER")
    private PlatformType source;
}
```

#### 4.1.6 UserAccountUpdateDTO.EmailVerifyRequest

```java
@Data
@Schema(description = "换绑邮箱请求 - 原邮箱验证")
public static class EmailVerifyRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "原邮箱不能为空")
    @Email(message = "邮箱格式不正确")
    @Schema(description = "原邮箱", example = "<EMAIL>", required = true)
    private String email;
}
```

### 4.2 响应模型

#### 4.2.1 Result

```java
@Data
@Schema(description = "通用响应结果")
public class Result<T> {
    @Schema(description = "状态码", example = "200")
    private Integer code;

    @Schema(description = "消息", example = "success")
    private String message;

    @Schema(description = "数据")
    private T data;

    public static <T> Result<T> success() {
        return success(null);
    }

    public static <T> Result<T> success(T data) {
        Result<T> result = new Result<>();
        result.setCode(200);
        result.setMessage("success");
        result.setData(data);
        return result;
    }

    public static <T> Result<T> error(String message) {
        Result<T> result = new Result<>();
        result.setCode(400);
        result.setMessage(message);
        return result;
    }

    public static <T> Result<T> error(Integer code, String message) {
        Result<T> result = new Result<>();
        result.setCode(code);
        result.setMessage(message);
        return result;
    }
}
```
