# 用户账户子域服务接口索引 - 用户管理

## 文档信息
- **版本**：2.0
- **作者**：系统设计团队
- **日期**：2025-06-20
- **状态**：更新中

## 1. 目的

本文档提供用户账户子域中用户管理服务接口的索引，帮助开发人员快速查找和导航相关接口文档。

## 2. 接口索引

| 序号 | 接口名称 | 接口路径 | 功能描述 | 文档位置 |
|------|---------|---------|---------|---------|
| 1 | 统一登录接口 | `POST /api/v1/login/unified` | 支持多种登录方式和多步骤登录流程的统一接口 | [2.1 统一登录接口](./5.1_服务接口-用户管理.md#21-统一登录接口) |
| 2 | 发送手机验证码 | `POST /api/v1/register/sendSmsCode` | 发送手机验证码，用于注册或登录 | [2.2 发送手机验证码](./5.1_服务接口-用户管理.md#22-发送手机验证码) |
| 3 | 发送邮箱验证码 | `POST /api/v1/register/sendEmailCode` | 发送邮箱验证码，用于注册或登录 | [2.3 发送邮箱验证码](./5.1_服务接口-用户管理.md#23-发送邮箱验证码) |
| 4 | 验证手机验证码 | `POST /api/v1/verify/smsCode` | 验证手机验证码，仅验证不登录 | [2.4 验证手机验证码](./5.1_服务接口-用户管理.md#24-验证手机验证码) |
| 5 | 验证邮箱验证码 | `POST /api/v1/verify/emailCode` | 验证邮箱验证码，仅验证不登录 | [2.5 验证邮箱验证码](./5.1_服务接口-用户管理.md#25-验证邮箱验证码) |
| 6 | 三方登录 | `GET /api/v1/login/{type}` | 重定向到第三方平台授权页面 | [2.6 三方登录](./5.1_服务接口-用户管理.md#26-三方登录) |
| 7 | 三方登录回调 | `GET /{type}/{platform}/{deviceId}/callback` | 处理第三方平台的授权回调 | [2.7 三方登录回调](./5.1_服务接口-用户管理.md#27-三方登录回调) |
| 8 | 退出登录 | `POST /api/v1/logout` | 退出当前用户的登录状态 | [2.8 退出登录](./5.1_服务接口-用户管理.md#28-退出登录) |
| 9 | 注销用户 | `POST /api/v1/deleteUser` | 注销当前用户账号 | [2.9 注销用户](./5.1_服务接口-用户管理.md#29-注销用户) |

## 3. 接口分类

### 3.1 验证码相关接口

- **发送手机验证码**：`POST /api/v1/register/sendSmsCode`
- **发送邮箱验证码**：`POST /api/v1/register/sendEmailCode`
- **验证手机验证码**：`POST /api/v1/verify/smsCode`
- **验证邮箱验证码**：`POST /api/v1/verify/emailCode`

### 3.2 登录注册接口

- **验证手机验证码并登录/注册**：`POST /api/v1/register/verifySmsCodeAndLogin`
- **验证邮箱验证码并登录/注册**：`POST /api/v1/register/verifyEmailCodeAndLogin`
- **账号密码登录**：`POST /api/v1/login/password`
- **创建新账号或使用已有账号**：`POST /api/v1/register/createNewAccountWithEmail`

### 3.3 三方登录接口

- **三方登录**：`GET /api/v1/login/{type}`
- **三方登录回调**：`GET /{type}/{platform}/{deviceId}/callback`

### 3.4 账户管理接口

- **退出登录**：`POST /api/v1/logout`
- **注销用户**：`POST /api/v1/deleteUser`

## 4. 最近添加的接口

| 接口名称 | 添加日期 | 版本 | 关联功能/需求 |
|---------|---------|------|-------------|
| 账号密码登录 | 2025-05-20 | 1.2 | 用户账号密码登录 |

## 5. 常用请求/响应模型

### 5.1 请求模型

- **SendSmsCodeRequest**：发送手机验证码请求
- **SendEmailCodeRequest**：发送邮箱验证码请求
- **VerifySmsCodeAndBindPhoneRequest**：验证手机验证码请求
- **VerifyEmailCodeAndLoginRequest**：验证邮箱验证码并登录请求
- **PasswordLoginRequest**：账号密码登录请求
- **CreateNewAccountWithEmailRequest**：创建新账号请求

### 5.2 响应模型

- **SendCodeResponse**：发送验证码响应
- **LoginResponse**：登录响应
- **ErrorResponse**：错误响应

## 变更历史
| 版本 | 日期 | 作者 | 变更描述 | 关联功能/需求 |
|-----|------|------|---------|-------------|
| 1.0 | 2025-05-20 | 系统设计团队 | 初始版本 | 用户管理服务接口索引 |
