# 用户账户子域服务接口 - 商户登录

## 文档信息
- **版本**：1.0
- **作者**：系统设计团队
- **日期**：2025-05-20
- **状态**：草稿

## 1. 目的

本文档描述用户账户子域中商户登录相关的服务接口，包括商户的注册、登录、验证码发送等操作。

## 2. 枚举定义

### 2.1 短信业务类型枚举（SmsAndEmailBusinessType）

```java
/**
 * 短信业务类型枚举
 */
@Schema(description = "业务类型用来发送手机短信或者邮箱key区分")
public enum SmsAndEmailBusinessType {
    /**
     * 注册/登录
     */
    REGISTER_LOGIN_CODE(0, "REGISTER_LOGIN_CODE", "注册"),

    /**
     * 换绑
     */
    PHONE_UNBIND_CODE(1, "PHONE_UNBIND_CODE", "换绑"),

    /**
     * 商户
     */
    MERCHANT_CODE(2, "MERCHANT_CODE", "商户");

    /**
     * 编码
     */
    private final int code;

    /**
     * 英文名称
     */
    private final String name;

    /**
     * 中文描述
     */
    private final String description;

    // 构造函数和getter方法省略
}
```

**说明**：
- 在请求中传递枚举值时，使用code值（0、1、2）
- 默认情况下，商户相关接口使用`MERCHANT_CODE(2)`

## 3. 接口列表

### 3.1 发送手机验证码

- **接口路径**：`POST /merchant/api/v1/register/sendSmsCode`
- **功能描述**：向指定的台湾手机号发送6位数字验证码，用于商户注册或登录验证
- **请求参数**：
  - 请求体：`SendSmsCodeRequest`
    - `phone`：手机号，字符串，必填，格式为台湾手机号（例如：**********）
    - `businessType`：短信业务类型，枚举，必填，默认为`2`（MERCHANT_CODE）
- **响应结果**：
  - 成功：`200 OK`，返回`SendCodeResponse`
    - `expireTime`：验证码过期时间戳（毫秒）
  - 失败：
    - `400 Bad Request`：请求参数错误，如手机号格式不正确或发送频率受限
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：无需认证
- **请求示例**：

```http
POST /merchant/api/v1/register/sendSmsCode HTTP/1.1
Content-Type: application/json

{
  "phone": "**********",
  "businessType": 2
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "expireTime": *************
  }
}
```

### 3.2 发送邮箱验证码

- **接口路径**：`POST /merchant/api/v1/register/sendEmailCode`
- **功能描述**：向指定的邮箱发送6位数字验证码，用于商户注册或登录验证
- **请求参数**：
  - 请求体：`SendEmailCodeRequest`
    - `email`：邮箱地址，字符串，必填，格式为有效的邮箱地址（例如：<EMAIL>）
    - `businessType`：短信业务类型，枚举，必填，默认为`2`（MERCHANT_CODE）
- **响应结果**：
  - 成功：`200 OK`，返回`SendCodeResponse`
    - `expireTime`：验证码过期时间戳（毫秒）
  - 失败：
    - `400 Bad Request`：请求参数错误，如邮箱格式不正确或发送频率受限
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：无需认证
- **请求示例**：

```http
POST /merchant/api/v1/register/sendEmailCode HTTP/1.1
Content-Type: application/json

{
  "email": "<EMAIL>",
  "businessType": 2
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "expireTime": *************
  }
}
```

### 3.3 验证手机验证码并创建商户账户

- **接口路径**：`POST /merchant/api/v1/register/verifySmsCodeAndCreateMerchant`
- **功能描述**：验证发送到手机的验证码并创建商户账户
- **请求参数**：
  - 请求体：`VerifySmsCodeAndBindPhoneRequest`
    - `phone`：手机号，字符串，必填，格式为台湾手机号（例如：**********）
    - `code`：6位数字验证码，字符串，必填
    - `platform`：平台类型，字符串，可选，如iOS、Android、Web等
    - `deviceInfo`：设备信息对象，可选，包含设备类型、设备ID等信息
      - `deviceUUID`：设备唯一标识，字符串
      - `deviceName`：设备名称，字符串
      - `deviceType`：设备类型，字符串
      - `loginIp`：登录IP，字符串（由系统自动填充）
    - `source`：用户来源枚举，默认为`SHOP_LOGIN`
    - `registrationType`：注册类型，默认为`PHONE_REGISTRATION`
    - `email`：邮箱地址，字符串，可选
    - `businessType`：短信业务类型，枚举，必填，默认为`2`（MERCHANT_CODE）
- **响应结果**：
  - 成功：`200 OK`，返回`LoginResponse`
    - `userId`：用户ID
    - `token`：Token信息
      - `accessToken`：访问Token
      - `refreshToken`：刷新Token
      - `expireTime`：Token过期时间
    - `userInfo`：用户信息
      - `userId`：用户ID
      - `nickname`：用户昵称
      - `phone`：手机号
      - `email`：邮箱
    - `responseType`：响应类型
  - 失败：
    - `400 Bad Request`：验证失败或请求参数错误
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：无需认证
- **请求示例**：

```http
POST /merchant/api/v1/register/verifySmsCodeAndCreateMerchant HTTP/1.1
Content-Type: application/json

{
  "phone": "**********",
  "code": "123456",
  "platform": "iOS",
  "deviceInfo": {
    "deviceUUID": "550e8400-e29b-41d4-a716-************",
    "deviceName": "iPhone 14 Pro",
    "deviceType": "iOS"
  }
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "userId": 123456,
    "token": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expireTime": "*************"
    },
    "userInfo": {
      "userId": 123456,
      "nickname": "商户_1234",
      "phone": "**********",
      "email": "<EMAIL>"
    },
    "responseType": "LOGIN_SUCCESS"
  }
}
```

### 3.4 验证邮箱验证码并登录

- **接口路径**：`POST /merchant/api/v1/register/verifyEmailCodeAndLogin`
- **功能描述**：验证发送到邮箱的验证码，验证成功且邮箱已注册时直接登录，验证成功但邮箱未注册时返回需要绑定手机号的状态
- **请求参数**：
  - 请求体：`VerifyEmailCodeAndLoginRequest`
    - `email`：邮箱地址，字符串，必填，格式为有效的邮箱地址（例如：<EMAIL>）
    - `code`：6位数字验证码，字符串，必填
    - `platform`：平台类型，字符串，可选，如iOS、Android、Web等
    - `deviceInfo`：设备信息对象，可选，包含设备类型、设备ID等信息
      - `deviceUUID`：设备唯一标识，字符串
      - `deviceName`：设备名称，字符串
      - `deviceType`：设备类型，字符串
      - `loginIp`：登录IP，字符串（由系统自动填充）
    - `businessType`：短信业务类型，枚举，必填，默认为`2`（MERCHANT_CODE）
- **响应结果**：
  - 成功：`200 OK`，返回`LoginResponse`或其他对象
  - 失败：
    - `400 Bad Request`：验证失败或请求参数错误
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：无需认证
- **请求示例**：

```http
POST /merchant/api/v1/register/verifyEmailCodeAndLogin HTTP/1.1
Content-Type: application/json

{
  "email": "<EMAIL>",
  "code": "123456",
  "platform": "iOS",
  "deviceInfo": {
    "deviceUUID": "550e8400-e29b-41d4-a716-************",
    "deviceName": "iPhone 14 Pro",
    "deviceType": "iOS"
  },
  "businessType": 2
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "userId": 123456,
    "token": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expireTime": "*************"
    },
    "userInfo": {
      "userId": 123456,
      "nickname": "商户_1234",
      "phone": "**********",
      "email": "<EMAIL>"
    },
    "responseType": "LOGIN_SUCCESS"
  }
}
```

### 3.5 验证邮箱验证码并创建商户账户

- **接口路径**：`POST /merchant/api/v1/register/verifyEmailCodeAndCreateMerchant`
- **功能描述**：验证发送到邮箱的验证码并创建商户账户
- **请求参数**：
  - 请求体：`VerifyEmailCodeAndLoginRequest`
    - `email`：邮箱地址，字符串，必填，格式为有效的邮箱地址（例如：<EMAIL>）
    - `code`：6位数字验证码，字符串，必填
    - `platform`：平台类型，字符串，可选，如iOS、Android、Web等
    - `deviceInfo`：设备信息对象，可选，包含设备类型、设备ID等信息
      - `deviceUUID`：设备唯一标识，字符串
      - `deviceName`：设备名称，字符串
      - `deviceType`：设备类型，字符串
      - `loginIp`：登录IP，字符串（由系统自动填充）
    - `businessType`：短信业务类型，枚举，必填，默认为`2`（MERCHANT_CODE）
- **响应结果**：
  - 成功：`200 OK`，返回`LoginResponse`
    - `userId`：用户ID
    - `token`：Token信息
      - `accessToken`：访问Token
      - `refreshToken`：刷新Token
      - `expireTime`：Token过期时间
    - `userInfo`：用户信息
      - `userId`：用户ID
      - `nickname`：用户昵称
      - `phone`：手机号
      - `email`：邮箱
    - `responseType`：响应类型
  - 失败：
    - `400 Bad Request`：验证失败或请求参数错误
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：无需认证
- **请求示例**：

```http
POST /merchant/api/v1/register/verifyEmailCodeAndCreateMerchant HTTP/1.1
Content-Type: application/json

{
  "email": "<EMAIL>",
  "code": "123456",
  "platform": "iOS",
  "deviceInfo": {
    "deviceUUID": "550e8400-e29b-41d4-a716-************",
    "deviceName": "iPhone 14 Pro",
    "deviceType": "iOS"
  },
  "businessType": 2
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "userId": 123456,
    "token": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expireTime": "*************"
    },
    "userInfo": {
      "userId": 123456,
      "nickname": "商户_1234",
      "phone": "",
      "email": "<EMAIL>"
    },
    "responseType": "LOGIN_SUCCESS"
  }
}
```

### 3.6 创建新账号或使用已有账号（邮箱+手机号）

- **接口路径**：`POST /merchant/api/v1/register/createNewAccountWithEmail`
- **功能描述**：根据accountUseFlag参数决定是使用已有账号还是创建新账号
- **请求参数**：
  - 请求体：`CreateNewAccountWithEmailRequest`
    - `email`：邮箱地址，字符串，必填，格式为有效的邮箱地址（例如：<EMAIL>）
    - `phone`：手机号，字符串，必填，格式为台湾手机号（例如：**********）
    - `accountUseFlag`：账号使用标志，布尔值，必填，true表示使用已有账号，false表示创建新账号
    - `platform`：平台类型，字符串，可选，如iOS、Android、Web等
    - `deviceInfo`：设备信息对象，可选，包含设备类型、设备ID等信息
      - `deviceUUID`：设备唯一标识，字符串
      - `deviceName`：设备名称，字符串
      - `deviceType`：设备类型，字符串
      - `loginIp`：登录IP，字符串（由系统自动填充）
    - `phoneToken`：手机令牌，字符串，可选，用于验证手机号的令牌
- **响应结果**：
  - 成功：`200 OK`，返回`LoginResponse`
    - `userId`：用户ID
    - `token`：Token信息
      - `accessToken`：访问Token
      - `refreshToken`：刷新Token
      - `expireTime`：Token过期时间
    - `userInfo`：用户信息
      - `userId`：用户ID
      - `nickname`：用户昵称
      - `phone`：手机号
      - `email`：邮箱
    - `responseType`：响应类型
  - 失败：
    - `400 Bad Request`：请求参数错误
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：无需认证
- **请求示例**：

```http
POST /merchant/api/v1/register/createNewAccountWithEmail HTTP/1.1
Content-Type: application/json

{
  "email": "<EMAIL>",
  "phone": "**********",
  "accountUseFlag": true,
  "platform": "iOS",
  "deviceInfo": {
    "deviceUUID": "550e8400-e29b-41d4-a716-************",
    "deviceName": "iPhone 14 Pro",
    "deviceType": "iOS"
  },
  "phoneToken": "a1b2c3d4e5f6g7h8:********"
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "userId": 123456,
    "token": {
      "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "expireTime": "*************"
    },
    "userInfo": {
      "userId": 123456,
      "nickname": "商户_1234",
      "phone": "**********",
      "email": "<EMAIL>"
    },
    "responseType": "LOGIN_SUCCESS"
  }
}
```

### 3.7 退出登录

- **接口路径**：`POST /merchant/api/v1/logout`
- **功能描述**：退出当前用户的登录状态
- **请求参数**：无
- **响应结果**：
  - 成功：`200 OK`
  - 失败：
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要用户认证
- **请求示例**：

```http
POST /merchant/api/v1/logout HTTP/1.1
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 3.8 验证邮箱验证码

- **接口路径**：`POST /merchant/api/v1/verify/emailCode`
- **功能描述**：验证发送到邮箱的验证码是否有效
- **请求参数**：
  - 请求体：`VerifyEmailCodeRequest`
    - `email`：邮箱地址，字符串，必填，格式为有效的邮箱地址（例如：<EMAIL>）
    - `code`：6位数字验证码，字符串，必填
    - `businessType`：短信业务类型，枚举，必填，默认为`2`（MERCHANT_CODE）
- **响应结果**：
  - 成功：`200 OK`
  - 失败：
    - `400 Bad Request`：验证失败或请求参数错误
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：无需认证
- **请求示例**：

```http
POST /merchant/api/v1/verify/emailCode HTTP/1.1
Content-Type: application/json

{
  "email": "<EMAIL>",
  "code": "123456",
  "businessType": 2
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 3.9 验证手机验证码

- **接口路径**：`POST /merchant/api/v1/verify/smsCode`
- **功能描述**：验证发送到手机的验证码是否有效
- **请求参数**：
  - 请求体：`VerifySmsCodeAndBindPhoneRequest`
    - `phone`：手机号，字符串，必填，格式为台湾手机号（例如：**********）
    - `code`：6位数字验证码，字符串，必填
    - `businessType`：短信业务类型，枚举，必填，默认为`2`（MERCHANT_CODE）
- **响应结果**：
  - 成功：`200 OK`
  - 失败：
    - `400 Bad Request`：验证失败或请求参数错误
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：无需认证
- **请求示例**：

```http
POST /merchant/api/v1/verify/smsCode HTTP/1.1
Content-Type: application/json

{
  "phone": "**********",
  "code": "123456",
  "businessType": 2
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 3.10 获取用户个人信息

- **接口路径**：`GET /merchant/api/v1/profile`
- **功能描述**：获取当前登录用户的个人信息，包括用户ID、昵称、头像、性别、手机号、邮箱等基本信息
- **请求参数**：无
- **响应结果**：
  - 成功：`200 OK`，返回`UserResponse`
    - `id`：用户ID
    - `username`：用户名
    - `email`：邮箱
    - `phone`：手机号
    - `nickname`：昵称
    - `gender`：性别
    - `avatar`：头像URL
    - `address`：常居地
    - `userStatus`：用户状态
    - `birthday`：生日
    - `createTime`：创建时间
    - `updateTime`：更新时间
    - `pwdFlag`：是否设置了密码
  - 失败：
    - `401 Unauthorized`：用户未登录或登录已过期
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要用户认证
- **请求示例**：

```http
GET /merchant/api/v1/profile HTTP/1.1
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 123456,
    "username": "merchant123",
    "email": "<EMAIL>",
    "phone": "**********",
    "nickname": "商户_1234",
    "gender": "MALE",
    "avatar": "https://example.com/avatars/default.png",
    "address": "台北市信义区",
    "userStatus": "ACTIVE",
    "birthday": "1990-01-01",
    "createTime": *************,
    "updateTime": *************,
    "pwdFlag": true
  }
}
```

### 3.11 验证手机号

- **接口路径**：`POST /merchant/api/v1/phone/verify`
- **功能描述**：验证用户手机号
- **请求参数**：
  - 请求体：`UserAccountUpdateDTO.PhoneVerifyRequest`
    - `phone`：手机号，字符串，必填，格式为台湾手机号（例如：**********）
- **响应结果**：
  - 成功：`200 OK`
  - 失败：
    - `400 Bad Request`：验证失败或请求参数错误
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要用户认证
- **请求示例**：

```http
POST /merchant/api/v1/phone/verify HTTP/1.1
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "phone": "**********"
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 3.12 换绑手机号

- **接口路径**：`POST /merchant/api/v1/phone/update`
- **功能描述**：更新用户绑定的手机号
- **请求参数**：
  - 请求体：`UserAccountUpdateDTO.PhoneUpdateRequest`
    - `newPhone`：新手机号，字符串，必填，格式为台湾手机号（例如：**********）
    - `code`：验证码，字符串，必填
    - `deviceId`：设备ID，字符串，可选
    - `businessType`：短信业务类型，枚举，必填，默认为`1`（PHONE_UNBIND_CODE）
    - `source`：平台类型，枚举，可选
- **响应结果**：
  - 成功：`200 OK`
  - 失败：
    - `400 Bad Request`：请求参数错误
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要用户认证
- **请求示例**：

```http
POST /merchant/api/v1/phone/update HTTP/1.1
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "newPhone": "**********",
  "code": "123456",
  "businessType": 1,
  "source": "MERCHANT"
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 3.13 绑定邮箱

- **接口路径**：`POST /merchant/api/v1/email/bind`
- **功能描述**：绑定邮箱到用户账户
- **请求参数**：
  - 请求体：`UserAccountUpdateDTO.EmailBindRequest`
    - `email`：邮箱，字符串，必填，格式为有效的邮箱地址（例如：<EMAIL>）
    - `code`：验证码，字符串，可选
    - `businessType`：短信业务类型，枚举，必填，默认为`1`（PHONE_UNBIND_CODE）
    - `source`：平台类型，枚举，可选
- **响应结果**：
  - 成功：`200 OK`
  - 失败：
    - `400 Bad Request`：请求参数错误
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要用户认证
- **请求示例**：

```http
POST /merchant/api/v1/email/bind HTTP/1.1
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "email": "<EMAIL>",
  "code": "123456",
  "businessType": 1,
  "source": "MERCHANT"
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 3.14 验证原邮箱

- **接口路径**：`POST /merchant/api/v1/email/verify`
- **功能描述**：验证用户原邮箱
- **请求参数**：
  - 请求体：`UserAccountUpdateDTO.EmailVerifyRequest`
    - `email`：原邮箱，字符串，必填，格式为有效的邮箱地址（例如：<EMAIL>）
- **响应结果**：
  - 成功：`200 OK`
  - 失败：
    - `400 Bad Request`：请求参数错误
    - `500 Internal Server Error`：服务器内部错误
- **权限要求**：需要用户认证
- **请求示例**：

```http
POST /merchant/api/v1/email/verify HTTP/1.1
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

{
  "email": "<EMAIL>"
}
```

- **响应示例**：

```json
{
  "code": 200,
  "message": "success",
  "data": null
}

## 4. 请求/响应模型

### 4.1 请求模型

#### 4.1.1 SendSmsCodeRequest

```java
@Data
@Schema(description = "发送手机验证码请求")
public class SendSmsCodeRequest {
    @NotBlank(message = "手机号不能为空")
    @Schema(description = "台湾手机号，以09开头，长度10位", example = "**********", required = true)
    private String phone;

    @NotNull
    @Schema(description = "短信业务类型：PHONE_REGISTER-手机号注册，PHONE_UNBIND-解绑手机号，MERCHANT_CODE-商户注册", example = "MERCHANT_CODE")
    private SmsAndEmailBusinessType businessType = SmsAndEmailBusinessType.MERCHANT_CODE;
}
```

#### 4.1.2 SendEmailCodeRequest

```java
@Data
@Schema(description = "发送邮箱验证码请求")
public class SendEmailCodeRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "邮箱不能为空")
    @Schema(description = "邮箱地址", example = "<EMAIL>", required = true)
    private String email;

    @NotNull
    @Schema(description = "短信业务类型：PHONE_REGISTER-手机号注册，PHONE_UNBIND-解绑手机号，MERCHANT_CODE-商户注册", example = "MERCHANT_CODE")
    private SmsAndEmailBusinessType businessType = SmsAndEmailBusinessType.MERCHANT_CODE;
}
```

#### 4.1.3 VerifySmsCodeAndBindPhoneRequest

```java
@Data
@Schema(description = "验证手机验证码请求")
public class VerifySmsCodeAndBindPhoneRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "手机号不能为空")
    @Schema(description = "台湾手机号，以09开头，长度10位", example = "**********", required = true)
    private String phone;

    @NotBlank(message = "验证码不能为空")
    @Schema(description = "6位数字验证码", example = "123456", required = true)
    private String code;

    @Schema(description = "平台类型", example = "CUSTOMER")
    private String platform;

    @Schema(description = "设备信息对象，包含设备类型、设备ID等信息", example = "{\"deviceUUID\":\"550e8400-e29b-41d4-a716-************\",\"deviceName\":\"iPhone 14 Pro\",\"deviceType\":\"iOS\"}")
    private AccountDeviceDTO deviceInfo;

    @Schema(description = "用户来源枚举")
    private UserSource source;

    @Schema(description = "注册类型：PHONE_REGISTRATION-手机号注册，EMAIL_BINDING-邮箱注册绑定手机号")
    private RegistrationType registrationType = RegistrationType.PHONE_REGISTRATION;

    @Schema(description = "邮箱地址", example = "<EMAIL>")
    private String email;

    @NotNull
    @Schema(description = "短信业务类型：PHONE_REGISTER-手机号注册，PHONE_UNBIND-解绑手机号，MERCHANT_CODE-商户注册", example = "MERCHANT_CODE")
    private SmsAndEmailBusinessType businessType = SmsAndEmailBusinessType.MERCHANT_CODE;
}
```

#### 4.1.4 VerifyEmailCodeAndLoginRequest

```java
@Data
@Schema(description = "验证邮箱验证码并登录请求")
public class VerifyEmailCodeAndLoginRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "邮箱不能为空")
    @Schema(description = "邮箱地址", example = "<EMAIL>", required = true)
    private String email;

    @NotBlank(message = "验证码不能为空")
    @Schema(description = "6位数字验证码", example = "123456", required = true)
    private String code;

    @Schema(description = "平台类型", example = "CUSTOMER")
    private String platform;

    @Schema(description = "设备信息对象，包含设备类型、设备ID等信息", example = "{\"deviceUUID\":\"550e8400-e29b-41d4-a716-************\",\"deviceName\":\"iPhone 14 Pro\",\"deviceType\":\"iOS\"}")
    private AccountDeviceDTO deviceInfo;

    @NotNull
    @Schema(description = "短信业务类型：PHONE_REGISTER-手机号注册，PHONE_UNBIND-解绑手机号，MERCHANT_CODE-商户注册", example = "MERCHANT_CODE")
    private SmsAndEmailBusinessType businessType = SmsAndEmailBusinessType.MERCHANT_CODE;
}
```

#### 4.1.5 AccountDeviceDTO

```java
@Data
@Schema(description = "账号设备信息")
public class AccountDeviceDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "设备唯一标识", example = "550e8400-e29b-41d4-a716-************")
    private String deviceUUID;

    @Schema(description = "设备名称", example = "iPhone 14 Pro")
    private String deviceName;

    @Schema(description = "设备类型", example = "iOS")
    private String deviceType;

    @Schema(description = "登录IP", example = "***********")
    private String loginIp;
}
```

#### 4.1.6 VerifyEmailCodeRequest

```java
@Data
@Schema(description = "验证邮箱验证码请求")
public class VerifyEmailCodeRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "邮箱不能为空")
    @Schema(description = "邮箱地址", example = "<EMAIL>", required = true)
    private String email;

    @NotBlank(message = "验证码不能为空")
    @Schema(description = "6位数字验证码", example = "123456", required = true)
    private String code;

    @NotNull
    @Schema(description = "短信业务类型：PHONE_REGISTER-手机号注册，PHONE_UNBIND-解绑手机号，MERCHANT_CODE-商户注册", example = "MERCHANT_CODE")
    private SmsAndEmailBusinessType businessType = SmsAndEmailBusinessType.MERCHANT_CODE;
}
```

#### 4.1.7 UserAccountUpdateDTO 相关请求模型

UserAccountUpdateDTO 包含多个内部类，用于账户信息更新相关操作：

- **PhoneVerifyRequest**：验证手机号请求
- **PhoneUpdateRequest**：换绑手机号请求
- **EmailBindRequest**：绑定邮箱请求
- **EmailVerifyRequest**：验证原邮箱请求

这些请求模型的具体字段和验证规则请参考用户账户管理相关文档。

### 4.2 响应模型

#### 4.2.1 SendCodeResponse

```java
@Data
@Schema(description = "发送验证码响应")
public class SendCodeResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "验证码过期时间戳（毫秒）", example = "*************")
    private Long expireTime;
}
```

#### 4.2.2 LoginResponse

```java
@Data
@Schema(description = "登录响应")
public class LoginResponse {
    @Schema(description = "用户ID", example = "123")
    private Long userId;

    @Schema(description = "Token信息")
    private TokenInfo token;

    @Schema(description = "用户信息")
    private UserInfo userInfo;

    @Schema(description = "响应类型")
    private ResponseType responseType;

    @Data
    @Schema(description = "Token信息")
    public static class TokenInfo {
        @Schema(description = "访问Token", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
        private String accessToken;

        @Schema(description = "刷新Token", example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
        private String refreshToken;

        @Schema(description = "Token过期时间", example = "*************")
        private String expireTime;
    }

    @Data
    @Schema(description = "用户信息")
    public static class UserInfo {
        @Schema(description = "用户ID", example = "123")
        Long userId;

        @Schema(description = "用户昵称", example = "美食客_1234")
        private String nickname;

        @Schema(description = "手机号", example = "**********")
        private String phone;

        @Schema(description = "邮箱", example = "<EMAIL>")
        private String email;
    }
}
```

#### 4.2.3 ErrorResponse

```java
@Data
@Schema(description = "错误响应")
public class ErrorResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "错误信息", example = "验证码不正确")
    private String message;

    public ErrorResponse(String message) {
        this.message = message;
    }
}
```

## 5. 错误码

| 错误码 | 错误消息 | 描述 |
|-------|---------|------|
| USER_NOT_FOUND | 用户不存在 | 指定的用户ID不存在 |
| PHONE_EXISTS | 手机号已存在 | 注册时使用的手机号已被占用 |
| EMAIL_EXISTS | 邮箱已存在 | 注册时使用的邮箱已被占用 |
| INVALID_CREDENTIAL | 无效的登录凭证 | 登录时提供的凭证无效 |
| INVALID_VERIFICATION_CODE | 验证码无效 | 提供的验证码无效或已过期 |
| TOO_MANY_ATTEMPTS | 尝试次数过多 | 发送验证码尝试次数过多，请稍后再试 |
| USER_LOGOUT_ERROR | 退出登录失败 | 退出登录过程中发生错误 |

## 变更历史
| 版本 | 日期 | 作者 | 变更描述 | 关联功能/需求 |
|-----|------|------|---------|-------------|
| 1.0 | 2025-05-20 | 系统设计团队 | 初始版本 | 商户登录功能 |
| 1.1 | 2025-06-01 | 系统设计团队 | 添加验证码验证、手机号和邮箱管理接口 | 商户账户管理功能 |
