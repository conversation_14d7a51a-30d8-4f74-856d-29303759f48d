-- 运营系统权限管理需求调整示例数据
-- 版本：2.0
-- 作者：系统设计团队
-- 日期：2025-01-27
-- 说明：根据新需求调整后的示例数据

-- =============================================
-- 1. 组织示例数据
-- =============================================

INSERT INTO `organization` (`org_name`, `org_code`, `org_type`, `parent_id`, `org_level`, `org_path`, `sort_order`, `description`) VALUES
('老猫点评科技有限公司', 'COMPANY_001', '公司', NULL, 1, '/1/', 1, '公司总部'),
('技术部', 'DEPT_TECH', '部门', 1, 2, '/1/2/', 1, '技术研发部门'),
('运营部', 'DEPT_OPS', '部门', 1, 2, '/1/3/', 2, '运营管理部门'),
('市场部', 'DEPT_MKT', '部门', 1, 2, '/1/4/', 3, '市场营销部门'),
('财务部', 'DEPT_FINANCE', '部门', 1, 2, '/1/5/', 4, '财务管理部门');

-- =============================================
-- 2. 权限入口示例数据
-- =============================================

INSERT INTO `permission_entry` (`entry_code`, `entry_name`, `description`, `sort_order`, `status`) VALUES
('SYSTEM_MANAGEMENT', '系统管理', '系统管理相关权限入口', 1, 'ACTIVE'),
('USER_MANAGEMENT', '用户管理', '用户管理相关权限入口', 2, 'ACTIVE'),
('ROLE_MANAGEMENT', '角色管理', '角色管理相关权限入口', 3, 'ACTIVE'),
('PERMISSION_MANAGEMENT', '权限管理', '权限管理相关权限入口', 4, 'ACTIVE'),
('ACCOUNT_MANAGEMENT', '账号管理', '账号管理相关权限入口', 5, 'ACTIVE'),
('MERCHANT_MANAGEMENT', '商户管理', '商户管理相关权限入口', 6, 'ACTIVE'),
('ORDER_MANAGEMENT', '订单管理', '订单管理相关权限入口', 7, 'ACTIVE'),
('PRODUCT_MANAGEMENT', '商品管理', '商品管理相关权限入口', 8, 'ACTIVE'),
('REPORT_MANAGEMENT', '报表管理', '报表管理相关权限入口', 9, 'ACTIVE'),
('FINANCE_MANAGEMENT', '财务管理', '财务管理相关权限入口', 10, 'ACTIVE');

-- =============================================
-- 3. 权限示例数据
-- =============================================

INSERT INTO `permission` (`permission_entry_id`, `permission_code`, `permission_name`, `permission_key`, `type`, `description`, `order_num`, `status`) VALUES
-- 系统管理权限
(1, 'SYSTEM_DASHBOARD', '系统仪表板', 'system:dashboard:view', 'MENU', '查看系统仪表板', 1, 'ACTIVE'),

-- 用户管理权限
(2, 'USER_VIEW', '查看用户', 'user:view', 'API', '查看用户列表和详情', 1, 'ACTIVE'),
(2, 'USER_CREATE', '创建用户', 'user:create', 'API', '创建新用户', 2, 'ACTIVE'),
(2, 'USER_UPDATE', '更新用户', 'user:update', 'API', '更新用户信息', 3, 'ACTIVE'),
(2, 'USER_DELETE', '删除用户', 'user:delete', 'API', '删除用户', 4, 'ACTIVE'),
(2, 'USER_EXPORT', '导出用户', 'user:export', 'API', '导出用户数据', 5, 'ACTIVE'),
(2, 'USER_RESET_PASSWORD', '重置密码', 'user:reset_password', 'API', '重置用户密码', 6, 'ACTIVE'),

-- 角色管理权限
(3, 'ROLE_VIEW', '查看角色', 'role:view', 'API', '查看角色列表和详情', 1, 'ACTIVE'),
(3, 'ROLE_MANAGE', '管理角色', 'role:manage', 'API', '新增、修改、删除角色', 2, 'ACTIVE'),

-- 权限管理权限
(4, 'PERMISSION_VIEW', '查看权限', 'permission:view', 'API', '查看权限列表和详情', 1, 'ACTIVE'),
(4, 'PERMISSION_MANAGE', '管理权限', 'permission:manage', 'API', '新增、修改权限信息、修改权限状态', 2, 'ACTIVE'),
(4, 'PERMISSION_ENTRY_CREATE', '创建权限入口', 'permission:entry:create', 'API', '创建权限入口', 3, 'ACTIVE'),

-- 账号管理权限
(5, 'ACCOUNT_VIEW', '查看账号', 'account:view', 'API', '查看账号列表和详情', 1, 'ACTIVE'),
(5, 'ACCOUNT_MANAGE', '管理账号', 'account:manage', 'API', '新增、修改账号信息、修改账号状态', 2, 'ACTIVE'),

-- 商户管理权限
(6, 'MERCHANT_VIEW', '查看商户', 'merchant:view', 'API', '查看商户列表和详情', 1, 'ACTIVE'),
(6, 'MERCHANT_CREATE', '创建商户', 'merchant:create', 'API', '创建新商户', 2, 'ACTIVE'),
(6, 'MERCHANT_UPDATE', '更新商户', 'merchant:update', 'API', '更新商户信息', 3, 'ACTIVE'),
(6, 'MERCHANT_DELETE', '删除商户', 'merchant:delete', 'API', '删除商户', 4, 'ACTIVE'),
(6, 'MERCHANT_AUDIT', '审核商户', 'merchant:audit', 'API', '审核商户资质', 5, 'ACTIVE'),
(6, 'MERCHANT_EXPORT', '导出商户', 'merchant:export', 'API', '导出商户数据', 6, 'ACTIVE'),

-- 订单管理权限
(7, 'ORDER_VIEW', '查看订单', 'order:view', 'API', '查看订单列表和详情', 1, 'ACTIVE'),
(7, 'ORDER_UPDATE', '更新订单', 'order:update', 'API', '更新订单信息', 2, 'ACTIVE'),
(7, 'ORDER_CANCEL', '取消订单', 'order:cancel', 'API', '取消订单', 3, 'ACTIVE'),
(7, 'ORDER_REFUND', '退款订单', 'order:refund', 'API', '处理订单退款', 4, 'ACTIVE'),
(7, 'ORDER_EXPORT', '导出订单', 'order:export', 'API', '导出订单数据', 5, 'ACTIVE'),

-- 商品管理权限
(8, 'PRODUCT_VIEW', '查看商品', 'product:view', 'API', '查看商品列表和详情', 1, 'ACTIVE'),
(8, 'PRODUCT_CREATE', '创建商品', 'product:create', 'API', '创建新商品', 2, 'ACTIVE'),
(8, 'PRODUCT_UPDATE', '更新商品', 'product:update', 'API', '更新商品信息', 3, 'ACTIVE'),
(8, 'PRODUCT_DELETE', '删除商品', 'product:delete', 'API', '删除商品', 4, 'ACTIVE'),
(8, 'PRODUCT_AUDIT', '审核商品', 'product:audit', 'API', '审核商品信息', 5, 'ACTIVE'),

-- 报表管理权限
(9, 'REPORT_USER_STATISTICS', '用户统计报表', 'report:user:statistics', 'API', '查看用户统计报表', 1, 'ACTIVE'),
(9, 'REPORT_ORDER_STATISTICS', '订单统计报表', 'report:order:statistics', 'API', '查看订单统计报表', 2, 'ACTIVE'),
(9, 'REPORT_MERCHANT_STATISTICS', '商户统计报表', 'report:merchant:statistics', 'API', '查看商户统计报表', 3, 'ACTIVE'),

-- 财务管理权限
(10, 'FINANCE_VIEW', '查看财务数据', 'finance:view', 'API', '查看财务报表', 1, 'ACTIVE'),
(10, 'FINANCE_EXPORT', '导出财务数据', 'finance:export', 'API', '导出财务报表', 2, 'ACTIVE'),
(10, 'SETTLEMENT_VIEW', '查看结算数据', 'settlement:view', 'API', '查看结算数据', 3, 'ACTIVE'),
(10, 'SETTLEMENT_CREATE', '创建结算', 'settlement:create', 'API', '创建结算记录', 4, 'ACTIVE'),
(10, 'SETTLEMENT_UPDATE', '更新结算', 'settlement:update', 'API', '更新结算信息', 5, 'ACTIVE');

-- =============================================
-- 4. 角色示例数据
-- =============================================

INSERT INTO `role` (`role_code`, `role_name`, `org_id`, `description`, `status`) VALUES
('SUPER_ADMIN', '超级管理员', 1, '系统超级管理员，拥有所有权限', 'ACTIVE'),
('SYSTEM_ADMIN', '系统管理员', 2, '系统管理员，负责用户、角色、权限管理', 'ACTIVE'),
('BUSINESS_ADMIN', '业务管理员', 3, '业务管理员，负责商户、订单、商品管理', 'ACTIVE'),
('MERCHANT_MANAGER', '商户经理', 3, '商户经理，负责商户管理和审核', 'ACTIVE'),
('ORDER_MANAGER', '订单经理', 3, '订单经理，负责订单处理和客服', 'ACTIVE'),
('FINANCE_MANAGER', '财务经理', 5, '财务经理，负责财务数据管理', 'ACTIVE'),
('REPORT_ANALYST', '报表分析师', 3, '报表分析师，负责数据分析和报表查看', 'ACTIVE'),
('CUSTOMER_SERVICE', '客服专员', 3, '客服专员，负责客户服务和简单订单处理', 'ACTIVE');

-- =============================================
-- 5. 角色权限关联示例数据
-- =============================================

-- 超级管理员拥有所有权限
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`) 
SELECT 1, id, 'GRANT' FROM permission WHERE status = 'ACTIVE';

-- 系统管理员权限
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`) VALUES
-- 系统管理
(2, 1, 'GRANT'), -- SYSTEM_DASHBOARD
-- 用户管理
(2, 2, 'GRANT'), -- USER_VIEW
(2, 3, 'GRANT'), -- USER_CREATE
(2, 4, 'GRANT'), -- USER_UPDATE
(2, 5, 'GRANT'), -- USER_DELETE
(2, 6, 'GRANT'), -- USER_EXPORT
(2, 7, 'GRANT'), -- USER_RESET_PASSWORD
-- 角色管理
(2, 8, 'GRANT'), -- ROLE_VIEW
(2, 9, 'GRANT'), -- ROLE_MANAGE
-- 权限管理
(2, 10, 'GRANT'), -- PERMISSION_VIEW
(2, 11, 'GRANT'), -- PERMISSION_MANAGE
(2, 12, 'GRANT'), -- PERMISSION_ENTRY_CREATE
-- 账号管理
(2, 13, 'GRANT'), -- ACCOUNT_VIEW
(2, 14, 'GRANT'); -- ACCOUNT_MANAGE

-- 业务管理员权限
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`) VALUES
-- 系统管理
(3, 1, 'GRANT'), -- SYSTEM_DASHBOARD
-- 商户管理
(3, 15, 'GRANT'), -- MERCHANT_VIEW
(3, 16, 'GRANT'), -- MERCHANT_CREATE
(3, 17, 'GRANT'), -- MERCHANT_UPDATE
(3, 18, 'GRANT'), -- MERCHANT_DELETE
(3, 19, 'GRANT'), -- MERCHANT_AUDIT
(3, 20, 'GRANT'), -- MERCHANT_EXPORT
-- 订单管理
(3, 21, 'GRANT'), -- ORDER_VIEW
(3, 22, 'GRANT'), -- ORDER_UPDATE
(3, 23, 'GRANT'), -- ORDER_CANCEL
(3, 24, 'GRANT'), -- ORDER_REFUND
(3, 25, 'GRANT'), -- ORDER_EXPORT
-- 商品管理
(3, 26, 'GRANT'), -- PRODUCT_VIEW
(3, 27, 'GRANT'), -- PRODUCT_CREATE
(3, 28, 'GRANT'), -- PRODUCT_UPDATE
(3, 29, 'GRANT'), -- PRODUCT_DELETE
(3, 30, 'GRANT'), -- PRODUCT_AUDIT
-- 报表管理
(3, 31, 'GRANT'), -- REPORT_USER_STATISTICS
(3, 32, 'GRANT'), -- REPORT_ORDER_STATISTICS
(3, 33, 'GRANT'); -- REPORT_MERCHANT_STATISTICS

-- 商户经理权限
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`) VALUES
(4, 1, 'GRANT'), -- SYSTEM_DASHBOARD
(4, 15, 'GRANT'), -- MERCHANT_VIEW
(4, 16, 'GRANT'), -- MERCHANT_CREATE
(4, 17, 'GRANT'), -- MERCHANT_UPDATE
(4, 19, 'GRANT'), -- MERCHANT_AUDIT
(4, 20, 'GRANT'), -- MERCHANT_EXPORT
(4, 33, 'GRANT'); -- REPORT_MERCHANT_STATISTICS

-- 财务经理权限
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`) VALUES
(6, 1, 'GRANT'), -- SYSTEM_DASHBOARD
(6, 34, 'GRANT'), -- FINANCE_VIEW
(6, 35, 'GRANT'), -- FINANCE_EXPORT
(6, 36, 'GRANT'), -- SETTLEMENT_VIEW
(6, 37, 'GRANT'), -- SETTLEMENT_CREATE
(6, 38, 'GRANT'); -- SETTLEMENT_UPDATE

-- 报表分析师权限
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`) VALUES
(7, 1, 'GRANT'), -- SYSTEM_DASHBOARD
(7, 31, 'GRANT'), -- REPORT_USER_STATISTICS
(7, 32, 'GRANT'), -- REPORT_ORDER_STATISTICS
(7, 33, 'GRANT'); -- REPORT_MERCHANT_STATISTICS

-- 客服专员权限
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`) VALUES
(8, 1, 'GRANT'), -- SYSTEM_DASHBOARD
(8, 21, 'GRANT'), -- ORDER_VIEW
(8, 22, 'GRANT'), -- ORDER_UPDATE
(8, 23, 'GRANT'); -- ORDER_CANCEL

-- =============================================
-- 6. 示例账号数据
-- =============================================

-- 更新现有business_account表的示例数据（假设已有记录）
UPDATE `business_account` SET 
    `employee_code` = 'EMP001',
    `real_name` = '张三',
    `phone` = '***********',
    `email` = '<EMAIL>',
    `status` = 'ACTIVE'
WHERE `id` = 1;

UPDATE `business_account` SET 
    `employee_code` = 'EMP002',
    `real_name` = '李四',
    `phone` = '***********',
    `email` = '<EMAIL>',
    `status` = 'ACTIVE'
WHERE `id` = 2;

-- =============================================
-- 7. 用户角色关联示例数据
-- =============================================

INSERT INTO `business_account_role` (`business_account_id`, `role_id`, `org_id`, `effective_date`, `expiry_date`, `status`) VALUES
(1, 1, 1, '2025-01-01', '2025-12-31', 'ACTIVE'), -- 用户1：超级管理员
(2, 2, 2, '2025-01-01', '2025-12-31', 'ACTIVE'), -- 用户2：系统管理员（技术部）
(3, 3, 3, '2025-01-01', '2025-12-31', 'ACTIVE'), -- 用户3：业务管理员（运营部）
(4, 4, 3, '2025-01-01', '2025-12-31', 'ACTIVE'), -- 用户4：商户经理（运营部）
(5, 6, 5, '2025-01-01', '2025-12-31', 'ACTIVE'), -- 用户5：财务经理（财务部）
(6, 7, 3, '2025-01-01', '2025-12-31', 'ACTIVE'), -- 用户6：报表分析师（运营部）
(7, 8, 3, '2025-01-01', '2025-12-31', 'ACTIVE'); -- 用户7：客服专员（运营部）

-- =============================================
-- 8. 示例修改日志数据
-- =============================================

INSERT INTO `modification_log` (`table_name`, `record_id`, `operation_type`, `field_name`, `old_value`, `new_value`, `operator_id`, `operator_name`, `operation_time`, `remark`) VALUES
('role', 1, 'CREATE', NULL, NULL, NULL, 1, '张三', '2025-01-27 10:00:00', '创建超级管理员角色'),
('role', 2, 'CREATE', NULL, NULL, NULL, 1, '张三', '2025-01-27 10:01:00', '创建系统管理员角色'),
('business_account', 1, 'CREATE', NULL, NULL, NULL, 1, '张三', '2025-01-27 10:02:00', '创建管理员账号'),
('business_account', 2, 'CREATE', NULL, NULL, NULL, 1, '张三', '2025-01-27 10:03:00', '创建系统管理员账号');
