-- 运营系统权限管理优化版本示例数据
-- 版本：1.0
-- 作者：系统设计团队
-- 日期：2025-01-27
-- 说明：基于优化版本的示例数据

-- =============================================
-- 1. 组织示例数据
-- =============================================

INSERT INTO `organization` (`org_name`, `org_code`, `org_type`, `parent_id`, `org_level`, `org_path`, `sort_order`, `description`) VALUES
('老猫点评科技有限公司', 'COMPANY_001', '公司', NULL, 1, '/1/', 1, '公司总部'),
('技术部', 'DEPT_TECH', '部门', 1, 2, '/1/2/', 1, '技术研发部门'),
('运营部', 'DEPT_OPS', '部门', 1, 2, '/1/3/', 2, '运营管理部门'),
('市场部', 'DEPT_MKT', '部门', 1, 2, '/1/4/', 3, '市场营销部门'),
('后端开发团队', 'TEAM_BACKEND', '团队', 2, 3, '/1/2/5/', 1, '后端开发团队'),
('前端开发团队', 'TEAM_FRONTEND', '团队', 2, 3, '/1/2/6/', 2, '前端开发团队'),
('商户运营团队', 'TEAM_MERCHANT_OPS', '团队', 3, 3, '/1/3/7/', 1, '商户运营团队'),
('用户运营团队', 'TEAM_USER_OPS', '团队', 3, 3, '/1/3/8/', 2, '用户运营团队');

-- =============================================
-- 2. 权限示例数据（树形结构，包含菜单、页面、按钮、API）
-- =============================================

-- 一级菜单权限
INSERT INTO `permission` (`permission_code`, `permission_name`, `type`, `parent_id`, `url`, `icon`, `order_num`, `description`) VALUES
('MENU_DASHBOARD', '仪表板', 'MENU', NULL, '/dashboard', 'dashboard', 1, '运营仪表板'),
('MENU_SYSTEM', '系统管理', 'MENU', NULL, '/system', 'system', 2, '系统管理菜单'),
('MENU_BUSINESS', '业务管理', 'MENU', NULL, '/business', 'business', 3, '业务管理菜单'),
('MENU_REPORT', '报表管理', 'MENU', NULL, '/reports', 'report', 4, '报表管理菜单');

-- 系统管理二级菜单权限
INSERT INTO `permission` (`permission_code`, `permission_name`, `type`, `parent_id`, `url`, `component_path`, `icon`, `order_num`, `description`) VALUES
('PAGE_USER_MANAGEMENT', '用户管理', 'PAGE', 2, '/system/users', 'System/User/Index', 'user', 1, '用户管理页面'),
('PAGE_ROLE_MANAGEMENT', '角色管理', 'PAGE', 2, '/system/roles', 'System/Role/Index', 'role', 2, '角色管理页面'),
('PAGE_PERMISSION_MANAGEMENT', '权限管理', 'PAGE', 2, '/system/permissions', 'System/Permission/Index', 'permission', 3, '权限管理页面'),
('PAGE_ORG_MANAGEMENT', '组织管理', 'PAGE', 2, '/system/organizations', 'System/Organization/Index', 'organization', 4, '组织管理页面');

-- 业务管理二级菜单权限
INSERT INTO `permission` (`permission_code`, `permission_name`, `type`, `parent_id`, `url`, `component_path`, `icon`, `order_num`, `description`) VALUES
('PAGE_MERCHANT_MANAGEMENT', '商户管理', 'PAGE', 3, '/business/merchants', 'Business/Merchant/Index', 'merchant', 1, '商户管理页面'),
('PAGE_ORDER_MANAGEMENT', '订单管理', 'PAGE', 3, '/business/orders', 'Business/Order/Index', 'order', 2, '订单管理页面'),
('PAGE_PRODUCT_MANAGEMENT', '商品管理', 'PAGE', 3, '/business/products', 'Business/Product/Index', 'product', 3, '商品管理页面');

-- 用户管理页面按钮权限
INSERT INTO `permission` (`permission_code`, `permission_name`, `type`, `parent_id`, `order_num`, `description`) VALUES
('BUTTON_USER_CREATE', '新增用户按钮', 'BUTTON', 5, 1, '用户管理页面新增按钮'),
('BUTTON_USER_EDIT', '编辑用户按钮', 'BUTTON', 5, 2, '用户管理页面编辑按钮'),
('BUTTON_USER_DELETE', '删除用户按钮', 'BUTTON', 5, 3, '用户管理页面删除按钮'),
('BUTTON_USER_EXPORT', '导出用户按钮', 'BUTTON', 5, 4, '用户管理页面导出按钮'),
('BUTTON_USER_RESET_PASSWORD', '重置密码按钮', 'BUTTON', 5, 5, '用户管理页面重置密码按钮');

-- API权限（支持两种命名方式）
INSERT INTO `permission` (`permission_code`, `permission_name`, `type`, `parent_id`, `url`, `http_method`, `order_num`, `description`) VALUES
-- 用户管理API权限
('USER:READ', '查看用户API', 'API', 5, '/api/users', 'GET', 1, '查看用户列表和详情API'),
('USER:CREATE', '创建用户API', 'API', 5, '/api/users', 'POST', 2, '创建新用户API'),
('USER:UPDATE', '更新用户API', 'API', 5, '/api/users/*', 'PUT', 3, '更新用户信息API'),
('USER:DELETE', '删除用户API', 'API', 5, '/api/users/*', 'DELETE', 4, '删除用户API'),
('USER:EXPORT', '导出用户API', 'API', 5, '/api/users/export', 'GET', 5, '导出用户数据API'),
('USER:RESET_PASSWORD', '重置密码API', 'API', 5, '/api/users/*/reset-password', 'POST', 6, '重置用户密码API'),

-- 角色管理API权限
('ROLE:READ', '查看角色API', 'API', 6, '/api/roles', 'GET', 1, '查看角色列表和详情API'),
('ROLE:CREATE', '创建角色API', 'API', 6, '/api/roles', 'POST', 2, '创建新角色API'),
('ROLE:UPDATE', '更新角色API', 'API', 6, '/api/roles/*', 'PUT', 3, '更新角色信息API'),
('ROLE:DELETE', '删除角色API', 'API', 6, '/api/roles/*', 'DELETE', 4, '删除角色API'),
('ROLE:ASSIGN', '分配角色API', 'API', 6, '/api/roles/*/assign', 'POST', 5, '为用户分配角色API'),

-- 商户管理API权限
('MERCHANT:READ', '查看商户API', 9, 'API', 9, '/api/merchants', 'GET', 1, '查看商户列表和详情API'),
('MERCHANT:CREATE', '创建商户API', 'API', 9, '/api/merchants', 'POST', 2, '创建新商户API'),
('MERCHANT:UPDATE', '更新商户API', 'API', 9, '/api/merchants/*', 'PUT', 3, '更新商户信息API'),
('MERCHANT:DELETE', '删除商户API', 'API', 9, '/api/merchants/*', 'DELETE', 4, '删除商户API'),
('MERCHANT:AUDIT', '审核商户API', 'API', 9, '/api/merchants/*/audit', 'POST', 5, '审核商户资质API'),
('MERCHANT:EXPORT', '导出商户API', 'API', 9, '/api/merchants/export', 'GET', 6, '导出商户数据API'),

-- 订单管理API权限
('ORDER:READ', '查看订单API', 'API', 10, '/api/orders', 'GET', 1, '查看订单列表和详情API'),
('ORDER:UPDATE', '更新订单API', 'API', 10, '/api/orders/*', 'PUT', 2, '更新订单信息API'),
('ORDER:CANCEL', '取消订单API', 'API', 10, '/api/orders/*/cancel', 'POST', 3, '取消订单API'),
('ORDER:REFUND', '退款订单API', 'API', 10, '/api/orders/*/refund', 'POST', 4, '处理订单退款API'),
('ORDER:EXPORT', '导出订单API', 'API', 10, '/api/orders/export', 'GET', 5, '导出订单数据API');

-- =============================================
-- 3. 角色示例数据
-- =============================================

INSERT INTO `role` (`role_code`, `role_name`, `org_id`, `description`) VALUES
('SUPER_ADMIN', '超级管理员', 1, '系统超级管理员，拥有所有权限'),
('SYSTEM_ADMIN', '系统管理员', 1, '系统管理员，负责用户、角色、权限管理'),
('BUSINESS_ADMIN', '业务管理员', 1, '业务管理员，负责商户、订单、商品管理'),
('MERCHANT_MANAGER', '商户经理', 7, '商户经理，负责商户管理和审核'),
('ORDER_MANAGER', '订单经理', 7, '订单经理，负责订单处理和客服'),
('REPORT_ANALYST', '报表分析师', 3, '报表分析师，负责数据分析和报表查看'),
('CUSTOMER_SERVICE', '客服专员', 8, '客服专员，负责客户服务和简单订单处理');

-- =============================================
-- 4. 角色权限关联示例数据
-- =============================================

-- 超级管理员拥有所有权限（这里只展示部分）
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`) VALUES
-- 菜单权限
(1, 1, 'GRANT'), -- MENU_DASHBOARD
(1, 2, 'GRANT'), -- MENU_SYSTEM
(1, 3, 'GRANT'), -- MENU_BUSINESS
(1, 4, 'GRANT'), -- MENU_REPORT
-- 页面权限
(1, 5, 'GRANT'), -- PAGE_USER_MANAGEMENT
(1, 6, 'GRANT'), -- PAGE_ROLE_MANAGEMENT
(1, 7, 'GRANT'), -- PAGE_PERMISSION_MANAGEMENT
(1, 8, 'GRANT'), -- PAGE_ORG_MANAGEMENT
(1, 9, 'GRANT'), -- PAGE_MERCHANT_MANAGEMENT
(1, 10, 'GRANT'), -- PAGE_ORDER_MANAGEMENT
(1, 11, 'GRANT'); -- PAGE_PRODUCT_MANAGEMENT

-- 系统管理员权限
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`) VALUES
-- 菜单和页面权限
(2, 1, 'GRANT'), -- MENU_DASHBOARD
(2, 2, 'GRANT'), -- MENU_SYSTEM
(2, 5, 'GRANT'), -- PAGE_USER_MANAGEMENT
(2, 6, 'GRANT'), -- PAGE_ROLE_MANAGEMENT
(2, 7, 'GRANT'), -- PAGE_PERMISSION_MANAGEMENT
(2, 8, 'GRANT'), -- PAGE_ORG_MANAGEMENT
-- 按钮权限
(2, 12, 'GRANT'), -- BUTTON_USER_CREATE
(2, 13, 'GRANT'), -- BUTTON_USER_EDIT
(2, 14, 'GRANT'), -- BUTTON_USER_DELETE
(2, 15, 'GRANT'), -- BUTTON_USER_EXPORT
(2, 16, 'GRANT'), -- BUTTON_USER_RESET_PASSWORD
-- API权限
(2, 17, 'GRANT'), -- USER:READ
(2, 18, 'GRANT'), -- USER:CREATE
(2, 19, 'GRANT'), -- USER:UPDATE
(2, 20, 'GRANT'), -- USER:DELETE
(2, 21, 'GRANT'), -- USER:EXPORT
(2, 22, 'GRANT'); -- USER:RESET_PASSWORD

-- 商户经理权限
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`) VALUES
-- 菜单和页面权限
(4, 1, 'GRANT'), -- MENU_DASHBOARD
(4, 3, 'GRANT'), -- MENU_BUSINESS
(4, 9, 'GRANT'), -- PAGE_MERCHANT_MANAGEMENT
-- API权限
(4, 28, 'GRANT'), -- MERCHANT:READ
(4, 29, 'GRANT'), -- MERCHANT:CREATE
(4, 30, 'GRANT'), -- MERCHANT:UPDATE
(4, 32, 'GRANT'), -- MERCHANT:AUDIT
(4, 33, 'GRANT'); -- MERCHANT:EXPORT

-- =============================================
-- 5. 数据权限规则示例数据
-- =============================================

INSERT INTO `data_permission_rule` (`rule_code`, `rule_name`, `rule_type`, `resource_type`, `filter_condition`, `description`) VALUES
('SELF_DATA', '本人数据', 'SELF', 'ALL', '{"created_by": "${current_user_id}"}', '只能查看自己创建的数据'),
('DEPT_DATA', '本部门数据', 'DEPT', 'ALL', '{"org_id": "${current_user_org_id}"}', '只能查看本部门的数据'),
('DEPT_SUB_DATA', '本部门及下级数据', 'DEPT_SUB', 'ALL', '{"org_path": "LIKE ${current_user_org_path}%"}', '可以查看本部门及下级部门的数据'),
('ORG_DATA', '本组织数据', 'ORG', 'ALL', '{"org_id": "${current_user_root_org_id}"}', '只能查看本组织的数据'),
('ALL_DATA', '全部数据', 'ALL', 'ALL', '{}', '可以查看所有数据'),
('MERCHANT_REGION_DATA', '区域商户数据', 'CUSTOM', 'MERCHANT', '{"region_code": "${current_user_region_code}"}', '只能查看指定区域的商户数据');

-- =============================================
-- 6. 角色数据权限关联示例数据
-- =============================================

INSERT INTO `role_data_permission` (`role_id`, `data_permission_rule_id`) VALUES
(1, 5), -- 超级管理员：全部数据
(2, 4), -- 系统管理员：本组织数据
(3, 4), -- 业务管理员：本组织数据
(4, 2), -- 商户经理：本部门数据
(5, 2), -- 订单经理：本部门数据
(6, 3), -- 报表分析师：本部门及下级数据
(7, 1); -- 客服专员：本人数据

-- =============================================
-- 7. 用户角色关联示例数据
-- =============================================

-- 假设已有business_account数据，这里展示角色分配
INSERT INTO `business_account_role` (`business_account_id`, `role_id`, `org_id`, `effective_date`, `expiry_date`, `status`) VALUES
(1, 1, 1, '2025-01-01', '2025-12-31', 'ACTIVE'), -- 用户1：超级管理员
(2, 2, 2, '2025-01-01', '2025-12-31', 'ACTIVE'), -- 用户2：系统管理员（技术部）
(3, 4, 7, '2025-01-01', '2025-12-31', 'ACTIVE'), -- 用户3：商户经理（商户运营团队）
(4, 5, 7, '2025-01-01', '2025-12-31', 'ACTIVE'), -- 用户4：订单经理（商户运营团队）
(5, 7, 8, '2025-01-01', '2025-12-31', 'ACTIVE'); -- 用户5：客服专员（用户运营团队）
