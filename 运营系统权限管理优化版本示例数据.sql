-- 运营系统权限管理优化版本示例数据（基于用户修改后的SQL）
-- 版本：2.0
-- 作者：系统设计团队
-- 日期：2025-01-27
-- 说明：基于用户修改后的简化版本生成示例数据

-- =============================================
-- 1. 组织示例数据
-- =============================================

INSERT INTO `organization` (`org_name`, `org_code`, `org_type`, `parent_id`, `org_level`, `org_path`, `sort_order`, `description`) VALUES
('老猫点评科技有限公司', 'COMPANY_001', '公司', NULL, 1, '/1/', 1, '公司总部'),
('技术部', 'DEPT_TECH', '部门', 1, 2, '/1/2/', 1, '技术研发部门'),
('运营部', 'DEPT_OPS', '部门', 1, 2, '/1/3/', 2, '运营管理部门'),
('市场部', 'DEPT_MKT', '部门', 1, 2, '/1/4/', 3, '市场营销部门'),
('财务部', 'DEPT_FINANCE', '部门', 1, 2, '/1/5/', 4, '财务管理部门'),
('后端开发团队', 'TEAM_BACKEND', '团队', 2, 3, '/1/2/6/', 1, '后端开发团队'),
('前端开发团队', 'TEAM_FRONTEND', '团队', 2, 3, '/1/2/7/', 2, '前端开发团队'),
('商户运营团队', 'TEAM_MERCHANT_OPS', '团队', 3, 3, '/1/3/8/', 1, '商户运营团队'),
('用户运营团队', 'TEAM_USER_OPS', '团队', 3, 3, '/1/3/9/', 2, '用户运营团队');

-- =============================================
-- 2. 权限示例数据（树形结构，包含菜单、页面、按钮、API）
-- =============================================

-- 一级菜单权限
INSERT INTO `permission` (`permission_code`, `permission_name`, `type`, `parent_id`, `url`, `icon`, `order_num`, `description`) VALUES
('MENU_DASHBOARD', '仪表板', 'MENU', NULL, '/dashboard', 'dashboard', 1, '运营仪表板'),
('MENU_SYSTEM', '系统管理', 'MENU', NULL, '/system', 'system', 2, '系统管理菜单'),
('MENU_BUSINESS', '业务管理', 'MENU', NULL, '/business', 'business', 3, '业务管理菜单'),
('MENU_REPORT', '报表管理', 'MENU', NULL, '/reports', 'report', 4, '报表管理菜单'),
('MENU_FINANCE', '财务管理', 'MENU', NULL, '/finance', 'finance', 5, '财务管理菜单');

-- 系统管理二级页面权限
INSERT INTO `permission` (`permission_code`, `permission_name`, `type`, `parent_id`, `url`, `component_path`, `order_num`, `description`) VALUES
('PAGE_USER_MANAGEMENT', '用户管理', 'PAGE', 2, '/system/users', 'System/User/Index', 1, '用户管理页面'),
('PAGE_ROLE_MANAGEMENT', '角色管理', 'PAGE', 2, '/system/roles', 'System/Role/Index', 2, '角色管理页面'),
('PAGE_PERMISSION_MANAGEMENT', '权限管理', 'PAGE', 2, '/system/permissions', 'System/Permission/Index', 3, '权限管理页面'),
('PAGE_ORG_MANAGEMENT', '组织管理', 'PAGE', 2, '/system/organizations', 'System/Organization/Index', 4, '组织管理页面');

-- 业务管理二级页面权限
INSERT INTO `permission` (`permission_code`, `permission_name`, `type`, `parent_id`, `url`, `component_path`, `order_num`, `description`) VALUES
('PAGE_MERCHANT_MANAGEMENT', '商户管理', 'PAGE', 3, '/business/merchants', 'Business/Merchant/Index', 1, '商户管理页面'),
('PAGE_ORDER_MANAGEMENT', '订单管理', 'PAGE', 3, '/business/orders', 'Business/Order/Index', 2, '订单管理页面'),
('PAGE_PRODUCT_MANAGEMENT', '商品管理', 'PAGE', 3, '/business/products', 'Business/Product/Index', 3, '商品管理页面');

-- 报表管理二级页面权限
INSERT INTO `permission` (`permission_code`, `permission_name`, `type`, `parent_id`, `url`, `component_path`, `order_num`, `description`) VALUES
('PAGE_USER_REPORT', '用户报表', 'PAGE', 4, '/reports/users', 'Report/User/Index', 1, '用户统计报表页面'),
('PAGE_ORDER_REPORT', '订单报表', 'PAGE', 4, '/reports/orders', 'Report/Order/Index', 2, '订单统计报表页面'),
('PAGE_MERCHANT_REPORT', '商户报表', 'PAGE', 4, '/reports/merchants', 'Report/Merchant/Index', 3, '商户统计报表页面');

-- 财务管理二级页面权限
INSERT INTO `permission` (`permission_code`, `permission_name`, `type`, `parent_id`, `url`, `component_path`, `order_num`, `description`) VALUES
('PAGE_FINANCE_REPORT', '财务报表', 'PAGE', 5, '/finance/reports', 'Finance/Report/Index', 1, '财务报表页面'),
('PAGE_SETTLEMENT_MANAGEMENT', '结算管理', 'PAGE', 5, '/finance/settlements', 'Finance/Settlement/Index', 2, '结算管理页面');

-- 用户管理页面按钮权限
INSERT INTO `permission` (`permission_code`, `permission_name`, `type`, `parent_id`, `order_num`, `description`) VALUES
('BUTTON_USER_CREATE', '新增用户按钮', 'BUTTON', 6, 1, '用户管理页面新增按钮'),
('BUTTON_USER_EDIT', '编辑用户按钮', 'BUTTON', 6, 2, '用户管理页面编辑按钮'),
('BUTTON_USER_DELETE', '删除用户按钮', 'BUTTON', 6, 3, '用户管理页面删除按钮'),
('BUTTON_USER_EXPORT', '导出用户按钮', 'BUTTON', 6, 4, '用户管理页面导出按钮'),
('BUTTON_USER_RESET_PASSWORD', '重置密码按钮', 'BUTTON', 6, 5, '用户管理页面重置密码按钮');

-- 角色管理页面按钮权限
INSERT INTO `permission` (`permission_code`, `permission_name`, `type`, `parent_id`, `order_num`, `description`) VALUES
('BUTTON_ROLE_CREATE', '新增角色按钮', 'BUTTON', 7, 1, '角色管理页面新增按钮'),
('BUTTON_ROLE_EDIT', '编辑角色按钮', 'BUTTON', 7, 2, '角色管理页面编辑按钮'),
('BUTTON_ROLE_DELETE', '删除角色按钮', 'BUTTON', 7, 3, '角色管理页面删除按钮'),
('BUTTON_ROLE_ASSIGN', '分配角色按钮', 'BUTTON', 7, 4, '角色管理页面分配按钮');

-- 商户管理页面按钮权限
INSERT INTO `permission` (`permission_code`, `permission_name`, `type`, `parent_id`, `order_num`, `description`) VALUES
('BUTTON_MERCHANT_CREATE', '新增商户按钮', 'BUTTON', 10, 1, '商户管理页面新增按钮'),
('BUTTON_MERCHANT_EDIT', '编辑商户按钮', 'BUTTON', 10, 2, '商户管理页面编辑按钮'),
('BUTTON_MERCHANT_AUDIT', '审核商户按钮', 'BUTTON', 10, 3, '商户管理页面审核按钮'),
('BUTTON_MERCHANT_EXPORT', '导出商户按钮', 'BUTTON', 10, 4, '商户管理页面导出按钮');

-- API权限（支持两种命名方式）
INSERT INTO `permission` (`permission_code`, `permission_name`, `type`, `parent_id`, `url`, `http_method`, `order_num`, `description`) VALUES
-- 用户管理API权限
('USER:READ', '查看用户API', 'API', 6, '/api/users', 'GET', 1, '查看用户列表和详情API'),
('USER:CREATE', '创建用户API', 'API', 6, '/api/users', 'POST', 2, '创建新用户API'),
('USER:UPDATE', '更新用户API', 'API', 6, '/api/users/*', 'PUT', 3, '更新用户信息API'),
('USER:DELETE', '删除用户API', 'API', 6, '/api/users/*', 'DELETE', 4, '删除用户API'),
('USER:EXPORT', '导出用户API', 'API', 6, '/api/users/export', 'GET', 5, '导出用户数据API'),
('USER:RESET_PASSWORD', '重置密码API', 'API', 6, '/api/users/*/reset-password', 'POST', 6, '重置用户密码API'),

-- 角色管理API权限
('ROLE:READ', '查看角色API', 'API', 7, '/api/roles', 'GET', 1, '查看角色列表和详情API'),
('ROLE:CREATE', '创建角色API', 'API', 7, '/api/roles', 'POST', 2, '创建新角色API'),
('ROLE:UPDATE', '更新角色API', 'API', 7, '/api/roles/*', 'PUT', 3, '更新角色信息API'),
('ROLE:DELETE', '删除角色API', 'API', 7, '/api/roles/*', 'DELETE', 4, '删除角色API'),
('ROLE:ASSIGN', '分配角色API', 'API', 7, '/api/roles/*/assign', 'POST', 5, '为用户分配角色API'),

-- 权限管理API权限
('PERMISSION:READ', '查看权限API', 'API', 8, '/api/permissions', 'GET', 1, '查看权限列表和详情API'),
('PERMISSION:CREATE', '创建权限API', 'API', 8, '/api/permissions', 'POST', 2, '创建新权限API'),
('PERMISSION:UPDATE', '更新权限API', 'API', 8, '/api/permissions/*', 'PUT', 3, '更新权限信息API'),
('PERMISSION:DELETE', '删除权限API', 'API', 8, '/api/permissions/*', 'DELETE', 4, '删除权限API'),

-- 组织管理API权限
('ORGANIZATION:READ', '查看组织API', 'API', 9, '/api/organizations', 'GET', 1, '查看组织列表和详情API'),
('ORGANIZATION:CREATE', '创建组织API', 'API', 9, '/api/organizations', 'POST', 2, '创建新组织API'),
('ORGANIZATION:UPDATE', '更新组织API', 'API', 9, '/api/organizations/*', 'PUT', 3, '更新组织信息API'),
('ORGANIZATION:DELETE', '删除组织API', 'API', 9, '/api/organizations/*', 'DELETE', 4, '删除组织API'),

-- 商户管理API权限
('MERCHANT:READ', '查看商户API', 'API', 10, '/api/merchants', 'GET', 1, '查看商户列表和详情API'),
('MERCHANT:CREATE', '创建商户API', 'API', 10, '/api/merchants', 'POST', 2, '创建新商户API'),
('MERCHANT:UPDATE', '更新商户API', 'API', 10, '/api/merchants/*', 'PUT', 3, '更新商户信息API'),
('MERCHANT:DELETE', '删除商户API', 'API', 10, '/api/merchants/*', 'DELETE', 4, '删除商户API'),
('MERCHANT:AUDIT', '审核商户API', 'API', 10, '/api/merchants/*/audit', 'POST', 5, '审核商户资质API'),
('MERCHANT:EXPORT', '导出商户API', 'API', 10, '/api/merchants/export', 'GET', 6, '导出商户数据API'),

-- 订单管理API权限
('ORDER:READ', '查看订单API', 'API', 11, '/api/orders', 'GET', 1, '查看订单列表和详情API'),
('ORDER:UPDATE', '更新订单API', 'API', 11, '/api/orders/*', 'PUT', 2, '更新订单信息API'),
('ORDER:CANCEL', '取消订单API', 'API', 11, '/api/orders/*/cancel', 'POST', 3, '取消订单API'),
('ORDER:REFUND', '退款订单API', 'API', 11, '/api/orders/*/refund', 'POST', 4, '处理订单退款API'),
('ORDER:EXPORT', '导出订单API', 'API', 11, '/api/orders/export', 'GET', 5, '导出订单数据API'),

-- 商品管理API权限
('PRODUCT:READ', '查看商品API', 'API', 12, '/api/products', 'GET', 1, '查看商品列表和详情API'),
('PRODUCT:CREATE', '创建商品API', 'API', 12, '/api/products', 'POST', 2, '创建新商品API'),
('PRODUCT:UPDATE', '更新商品API', 'API', 12, '/api/products/*', 'PUT', 3, '更新商品信息API'),
('PRODUCT:DELETE', '删除商品API', 'API', 12, '/api/products/*', 'DELETE', 4, '删除商品API'),
('PRODUCT:AUDIT', '审核商品API', 'API', 12, '/api/products/*/audit', 'POST', 5, '审核商品信息API'),

-- 报表管理API权限
('REPORT:USER_STATISTICS', '用户统计报表API', 'API', 13, '/api/reports/user-statistics', 'GET', 1, '查看用户统计报表API'),
('REPORT:ORDER_STATISTICS', '订单统计报表API', 'API', 14, '/api/reports/order-statistics', 'GET', 1, '查看订单统计报表API'),
('REPORT:MERCHANT_STATISTICS', '商户统计报表API', 'API', 15, '/api/reports/merchant-statistics', 'GET', 1, '查看商户统计报表API'),

-- 财务管理API权限
('FINANCE:READ', '查看财务数据API', 'API', 16, '/api/finance/reports', 'GET', 1, '查看财务报表API'),
('FINANCE:EXPORT', '导出财务数据API', 'API', 16, '/api/finance/reports/export', 'GET', 2, '导出财务报表API'),
('SETTLEMENT:READ', '查看结算数据API', 'API', 17, '/api/finance/settlements', 'GET', 1, '查看结算数据API'),
('SETTLEMENT:CREATE', '创建结算API', 'API', 17, '/api/finance/settlements', 'POST', 2, '创建结算记录API'),
('SETTLEMENT:UPDATE', '更新结算API', 'API', 17, '/api/finance/settlements/*', 'PUT', 3, '更新结算信息API');

-- =============================================
-- 3. 角色示例数据
-- =============================================

INSERT INTO `role` (`role_code`, `role_name`, `org_id`, `description`) VALUES
('SUPER_ADMIN', '超级管理员', 1, '系统超级管理员，拥有所有权限'),
('SYSTEM_ADMIN', '系统管理员', 2, '系统管理员，负责用户、角色、权限管理'),
('BUSINESS_ADMIN', '业务管理员', 3, '业务管理员，负责商户、订单、商品管理'),
('MERCHANT_MANAGER', '商户经理', 8, '商户经理，负责商户管理和审核'),
('ORDER_MANAGER', '订单经理', 8, '订单经理，负责订单处理和客服'),
('FINANCE_MANAGER', '财务经理', 5, '财务经理，负责财务数据管理'),
('REPORT_ANALYST', '报表分析师', 3, '报表分析师，负责数据分析和报表查看'),
('CUSTOMER_SERVICE', '客服专员', 9, '客服专员，负责客户服务和简单订单处理');

-- =============================================
-- 4. 角色权限关联示例数据
-- =============================================

-- 超级管理员拥有所有菜单权限
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`) VALUES
-- 菜单权限
(1, 1, 'GRANT'), -- MENU_DASHBOARD
(1, 2, 'GRANT'), -- MENU_SYSTEM
(1, 3, 'GRANT'), -- MENU_BUSINESS
(1, 4, 'GRANT'), -- MENU_REPORT
(1, 5, 'GRANT'), -- MENU_FINANCE
-- 页面权限
(1, 6, 'GRANT'), -- PAGE_USER_MANAGEMENT
(1, 7, 'GRANT'), -- PAGE_ROLE_MANAGEMENT
(1, 8, 'GRANT'), -- PAGE_PERMISSION_MANAGEMENT
(1, 9, 'GRANT'), -- PAGE_ORG_MANAGEMENT
(1, 10, 'GRANT'), -- PAGE_MERCHANT_MANAGEMENT
(1, 11, 'GRANT'), -- PAGE_ORDER_MANAGEMENT
(1, 12, 'GRANT'), -- PAGE_PRODUCT_MANAGEMENT
(1, 13, 'GRANT'), -- PAGE_USER_REPORT
(1, 14, 'GRANT'), -- PAGE_ORDER_REPORT
(1, 15, 'GRANT'), -- PAGE_MERCHANT_REPORT
(1, 16, 'GRANT'), -- PAGE_FINANCE_REPORT
(1, 17, 'GRANT'); -- PAGE_SETTLEMENT_MANAGEMENT

-- 系统管理员权限
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`) VALUES
-- 菜单和页面权限
(2, 1, 'GRANT'), -- MENU_DASHBOARD
(2, 2, 'GRANT'), -- MENU_SYSTEM
(2, 6, 'GRANT'), -- PAGE_USER_MANAGEMENT
(2, 7, 'GRANT'), -- PAGE_ROLE_MANAGEMENT
(2, 8, 'GRANT'), -- PAGE_PERMISSION_MANAGEMENT
(2, 9, 'GRANT'), -- PAGE_ORG_MANAGEMENT
-- 按钮权限
(2, 18, 'GRANT'), -- BUTTON_USER_CREATE
(2, 19, 'GRANT'), -- BUTTON_USER_EDIT
(2, 20, 'GRANT'), -- BUTTON_USER_DELETE
(2, 21, 'GRANT'), -- BUTTON_USER_EXPORT
(2, 22, 'GRANT'), -- BUTTON_USER_RESET_PASSWORD
(2, 23, 'GRANT'), -- BUTTON_ROLE_CREATE
(2, 24, 'GRANT'), -- BUTTON_ROLE_EDIT
(2, 25, 'GRANT'), -- BUTTON_ROLE_DELETE
(2, 26, 'GRANT'); -- BUTTON_ROLE_ASSIGN

-- 业务管理员权限
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`) VALUES
-- 菜单和页面权限
(3, 1, 'GRANT'), -- MENU_DASHBOARD
(3, 3, 'GRANT'), -- MENU_BUSINESS
(3, 4, 'GRANT'), -- MENU_REPORT
(3, 10, 'GRANT'), -- PAGE_MERCHANT_MANAGEMENT
(3, 11, 'GRANT'), -- PAGE_ORDER_MANAGEMENT
(3, 12, 'GRANT'), -- PAGE_PRODUCT_MANAGEMENT
(3, 13, 'GRANT'), -- PAGE_USER_REPORT
(3, 14, 'GRANT'), -- PAGE_ORDER_REPORT
(3, 15, 'GRANT'), -- PAGE_MERCHANT_REPORT
-- 按钮权限
(3, 27, 'GRANT'), -- BUTTON_MERCHANT_CREATE
(3, 28, 'GRANT'), -- BUTTON_MERCHANT_EDIT
(3, 29, 'GRANT'), -- BUTTON_MERCHANT_AUDIT
(3, 30, 'GRANT'); -- BUTTON_MERCHANT_EXPORT

-- 商户经理权限
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`) VALUES
-- 菜单和页面权限
(4, 1, 'GRANT'), -- MENU_DASHBOARD
(4, 3, 'GRANT'), -- MENU_BUSINESS
(4, 10, 'GRANT'), -- PAGE_MERCHANT_MANAGEMENT
(4, 15, 'GRANT'), -- PAGE_MERCHANT_REPORT
-- 按钮权限
(4, 27, 'GRANT'), -- BUTTON_MERCHANT_CREATE
(4, 28, 'GRANT'), -- BUTTON_MERCHANT_EDIT
(4, 29, 'GRANT'), -- BUTTON_MERCHANT_AUDIT
(4, 30, 'GRANT'); -- BUTTON_MERCHANT_EXPORT

-- 财务经理权限
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`) VALUES
-- 菜单和页面权限
(6, 1, 'GRANT'), -- MENU_DASHBOARD
(6, 5, 'GRANT'), -- MENU_FINANCE
(6, 16, 'GRANT'), -- PAGE_FINANCE_REPORT
(6, 17, 'GRANT'); -- PAGE_SETTLEMENT_MANAGEMENT

-- 报表分析师权限
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`) VALUES
-- 菜单和页面权限
(7, 1, 'GRANT'), -- MENU_DASHBOARD
(7, 4, 'GRANT'), -- MENU_REPORT
(7, 13, 'GRANT'), -- PAGE_USER_REPORT
(7, 14, 'GRANT'), -- PAGE_ORDER_REPORT
(7, 15, 'GRANT'); -- PAGE_MERCHANT_REPORT

-- 客服专员权限
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`) VALUES
-- 菜单和页面权限
(8, 1, 'GRANT'), -- MENU_DASHBOARD
(8, 3, 'GRANT'), -- MENU_BUSINESS
(8, 11, 'GRANT'); -- PAGE_ORDER_MANAGEMENT

-- =============================================
-- 5. 用户角色关联示例数据
-- =============================================

-- 假设已有business_account数据，这里展示角色分配
INSERT INTO `business_account_role` (`business_account_id`, `role_id`, `org_id`, `effective_date`, `expiry_date`, `status`) VALUES
(1, 1, 1, '2025-01-01', '2025-12-31', 'ACTIVE'), -- 用户1：超级管理员
(2, 2, 2, '2025-01-01', '2025-12-31', 'ACTIVE'), -- 用户2：系统管理员（技术部）
(3, 3, 3, '2025-01-01', '2025-12-31', 'ACTIVE'), -- 用户3：业务管理员（运营部）
(4, 4, 8, '2025-01-01', '2025-12-31', 'ACTIVE'), -- 用户4：商户经理（商户运营团队）
(5, 5, 8, '2025-01-01', '2025-12-31', 'ACTIVE'), -- 用户5：订单经理（商户运营团队）
(6, 6, 5, '2025-01-01', '2025-12-31', 'ACTIVE'), -- 用户6：财务经理（财务部）
(7, 7, 3, '2025-01-01', '2025-12-31', 'ACTIVE'), -- 用户7：报表分析师（运营部）
(8, 8, 9, '2025-01-01', '2025-12-31', 'ACTIVE'); -- 用户8：客服专员（用户运营团队）
