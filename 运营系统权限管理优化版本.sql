-- 运营系统权限管理优化版本（基于用户设计）
-- 版本：1.0
-- 作者：系统设计团队
-- 日期：2025-01-27
-- 说明：基于用户提供的设计进行优化和修复

-- =============================================
-- 1. 组织表（修复字段名错误）
-- =============================================

CREATE TABLE `organization` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `parent_id` bigint DEFAULT NULL COMMENT '上级组织ID',
  `org_name` varchar(100) NOT NULL COMMENT '组织名称',
  `org_code` varchar(50) NOT NULL COMMENT '唯一编码',
  `org_type` varchar(20) DEFAULT NULL COMMENT '组织类型（如公司、部门）',
  `org_level` int NOT NULL DEFAULT '1' COMMENT '组织层级，从1开始',
  `org_path` varchar(500) DEFAULT NULL COMMENT '组织路径，如：/1/2/3/',
  `sort_order` int DEFAULT '0' COMMENT '排序顺序',
  `leader_id` bigint DEFAULT NULL COMMENT '负责人ID，关联business_account表',
  `description` varchar(500) DEFAULT NULL COMMENT '组织描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_org_code` (`org_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_org_type` (`org_type`),
  KEY `idx_org_level` (`org_level`),
  KEY `idx_leader_id` (`leader_id`),
  CONSTRAINT `fk_organization_parent` FOREIGN KEY (`parent_id`) REFERENCES `organization` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='组织表';

-- =============================================
-- 2. 商户账户补充组织ID字段
-- =============================================

ALTER TABLE `business_account`
  ADD COLUMN `org_id` BIGINT DEFAULT NULL COMMENT '所属组织ID',
  ADD KEY `idx_org_id` (`org_id`);

-- =============================================
-- 3. 权限表（支持菜单、页面、按钮、API权限）
-- =============================================

CREATE TABLE `permission` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `permission_code` VARCHAR(100) NOT NULL COMMENT '权限唯一编码，支持两种格式：1.模块:动作(如USER:READ) 2.功能编码(如MENU_DASHBOARD)',
  `permission_name` VARCHAR(100) NOT NULL COMMENT '权限名称',
  `type` varchar(50) NOT NULL DEFAULT 'API' COMMENT '权限类型：MENU-菜单, PAGE-页面, BUTTON-按钮, API-接口',
  `parent_id` BIGINT DEFAULT NULL COMMENT '父权限ID，构成树形结构',
  `url` VARCHAR(255) DEFAULT NULL COMMENT '页面路径或API路径，菜单和页面用',
  `http_method` VARCHAR(10) DEFAULT NULL COMMENT 'HTTP方法：GET、POST、PUT、DELETE等',
  `icon` VARCHAR(100) DEFAULT NULL COMMENT '菜单图标（菜单类型时）',
  `component_path` VARCHAR(200) DEFAULT NULL COMMENT '组件路径（页面类型时）',
  `order_num` INT DEFAULT 0 COMMENT '排序字段',
  `is_visible` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否可见：0-隐藏，1-显示',
  `description` VARCHAR(500) DEFAULT NULL COMMENT '权限描述',
  `active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` VARCHAR(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` VARCHAR(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_permission_code` (`permission_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_type` (`type`),
  KEY `idx_order_num` (`order_num`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表，支持菜单、页面、按钮、API权限';

-- =============================================
-- 4. 角色表
-- =============================================

CREATE TABLE `role` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_code` VARCHAR(50) NOT NULL COMMENT '角色编码，唯一，例如：ADMIN、SHOP_MANAGER',
  `role_name` VARCHAR(100) NOT NULL COMMENT '角色名称',
  `org_id` bigint DEFAULT NULL COMMENT '所属组织ID',
  `description` VARCHAR(255) DEFAULT NULL COMMENT '角色描述',
  `active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` VARCHAR(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` VARCHAR(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_code` (`role_code`),
  KEY `idx_org_id` (`org_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- =============================================
-- 5. 角色-权限关联表
-- =============================================

CREATE TABLE `role_permission` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_id` BIGINT NOT NULL COMMENT '角色ID',
  `permission_id` BIGINT NOT NULL COMMENT '权限ID',
  `grant_type` VARCHAR(20) NOT NULL DEFAULT 'GRANT' COMMENT '授权类型：GRANT-授权，DENY-拒绝',
  `active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` VARCHAR(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` VARCHAR(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_permission_id` (`permission_id`),
  KEY `idx_grant_type` (`grant_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- =============================================
-- 6. 商户账户-角色关联表（优化版本）
-- =============================================

CREATE TABLE IF NOT EXISTS `business_account_role` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `business_account_id` BIGINT NOT NULL COMMENT '商户账户ID',
  `role_id` BIGINT NOT NULL COMMENT '角色ID',
  `org_id` BIGINT DEFAULT NULL COMMENT '组织ID，角色在特定组织内有效',
  `effective_date` DATE DEFAULT NULL COMMENT '生效日期',
  `expiry_date` DATE DEFAULT NULL COMMENT '失效日期',
  `status` VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-生效，INACTIVE-失效，EXPIRED-过期',
  `active` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` VARCHAR(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` VARCHAR(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_business_account_role` (`business_account_id`, `role_id`),
  KEY `idx_business_account_id` (`business_account_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_org_id` (`org_id`),
  KEY `idx_status` (`status`),
  KEY `idx_effective_date` (`effective_date`),
  KEY `idx_expiry_date` (`expiry_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商户运营账户角色关联表';

-- =============================================
-- 9. 权限操作日志表
-- =============================================

CREATE TABLE `permission_operation_log` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `operation_type` VARCHAR(20) NOT NULL COMMENT '操作类型：GRANT-授权，REVOKE-撤销，LOGIN-登录，ACCESS-访问',
  `operator_id` BIGINT NOT NULL COMMENT '操作人ID',
  `target_user_id` BIGINT DEFAULT NULL COMMENT '目标用户ID',
  `target_role_id` BIGINT DEFAULT NULL COMMENT '目标角色ID',
  `permission_code` VARCHAR(100) DEFAULT NULL COMMENT '权限编码',
  `resource_path` VARCHAR(200) DEFAULT NULL COMMENT '访问资源路径',
  `operation_result` VARCHAR(20) NOT NULL COMMENT '操作结果：SUCCESS-成功，FAILURE-失败',
  `failure_reason` VARCHAR(500) DEFAULT NULL COMMENT '失败原因',
  `client_ip` VARCHAR(50) DEFAULT NULL COMMENT '客户端IP',
  `user_agent` VARCHAR(500) DEFAULT NULL COMMENT '用户代理',
  `operation_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_target_user_id` (`target_user_id`),
  KEY `idx_target_role_id` (`target_role_id`),
  KEY `idx_operation_time` (`operation_time`),
  KEY `idx_operation_result` (`operation_result`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限操作日志表';
