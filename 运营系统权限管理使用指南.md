# 运营系统权限管理使用指南

## 文档信息
- **版本**：1.0
- **作者**：系统设计团队
- **日期**：2025-01-27
- **状态**：草稿

## 1. 概述

本文档提供运营系统权限管理的详细使用指南，包括权限配置、角色管理、菜单权限设置、数据权限配置等操作说明。

## 2. 权限模型说明

### 2.1 RBAC权限模型

系统采用基于角色的访问控制（RBAC）模型：

```
用户(User) -> 角色(Role) -> 权限(Permission) -> 资源(Resource)
```

- **用户**：系统的操作者，通过business_account表管理
- **角色**：权限的集合，如管理员、操作员等
- **权限**：对特定资源的操作许可，如USER:READ、ORDER:CREATE
- **资源**：系统中的功能模块、菜单、API接口等

### 2.2 权限编码规范

权限编码采用"模块:动作"格式：

- **模块**：功能模块名称，如USER、ORDER、MERCHANT
- **动作**：具体操作，如READ、CREATE、UPDATE、DELETE、EXPORT、AUDIT
- **示例**：
  - `USER:READ` - 查看用户
  - `USER:CREATE` - 创建用户
  - `ORDER:EXPORT` - 导出订单
  - `MERCHANT:AUDIT` - 审核商户

## 3. 权限配置操作

### 3.1 创建权限分组

```sql
-- 创建权限分组
INSERT INTO `permission_group` (
    `group_code`, `group_name`, `parent_id`, `group_level`, 
    `sort_order`, `description`, `status`
) VALUES (
    'FINANCE', '财务管理', NULL, 1, 5, '财务管理相关权限', 'ACTIVE'
);
```

### 3.2 创建权限

```sql
-- 创建权限
INSERT INTO `permission` (
    `permission_code`, `permission_name`, `permission_group_id`, 
    `resource_type`, `resource_path`, `http_method`, 
    `description`, `status`
) VALUES (
    'FINANCE:READ', '查看财务', 11, 'API', '/api/finance', 'GET', 
    '查看财务数据', 'ACTIVE'
);
```

### 3.3 创建角色

```sql
-- 创建角色
INSERT INTO `role` (`role_code`, `role_name`, `description`, `status`) 
VALUES ('FINANCE_MANAGER', '财务经理', '财务部门经理角色', 'ACTIVE');
```

### 3.4 为角色分配权限

```sql
-- 为角色分配权限
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`) 
VALUES (8, 37, 'GRANT');
```

### 3.5 为用户分配角色

```sql
-- 为用户分配角色
INSERT INTO `business_account_role` (
    `business_account_id`, `role_id`, `organization_id`, 
    `effective_date`, `expiry_date`, `status`
) VALUES (
    1001, 8, 1, '2025-01-27', '2025-12-31', 'ACTIVE'
);
```

## 4. 菜单权限配置

### 4.1 创建菜单

```sql
-- 创建一级菜单（目录）
INSERT INTO `menu` (
    `menu_code`, `menu_name`, `menu_type`, `parent_id`, `menu_level`,
    `route_path`, `component_path`, `icon`, `sort_order`, 
    `description`, `status`
) VALUES (
    'FINANCE_MANAGEMENT', '财务管理', 'DIRECTORY', NULL, 1,
    '/finance', NULL, 'finance', 5, '财务管理目录', 'ACTIVE'
);

-- 创建二级菜单（页面）
INSERT INTO `menu` (
    `menu_code`, `menu_name`, `menu_type`, `parent_id`, `menu_level`,
    `route_path`, `component_path`, `icon`, `sort_order`, 
    `description`, `status`
) VALUES (
    'FINANCE_REPORT', '财务报表', 'MENU', 20, 2,
    '/finance/reports', 'Finance/Report/Index', 'finance-report', 1, 
    '财务报表页面', 'ACTIVE'
);

-- 创建按钮权限
INSERT INTO `menu` (
    `menu_code`, `menu_name`, `menu_type`, `parent_id`, `menu_level`,
    `route_path`, `component_path`, `icon`, `sort_order`, 
    `description`, `status`
) VALUES (
    'FINANCE_EXPORT_BTN', '导出财务数据按钮', 'BUTTON', 21, 3,
    NULL, NULL, NULL, 1, '财务报表页面导出按钮', 'ACTIVE'
);
```

### 4.2 关联菜单与权限

```sql
-- 关联菜单与权限
INSERT INTO `menu_permission` (`menu_id`, `permission_id`) VALUES
(21, 37), -- FINANCE_REPORT -> FINANCE:READ
(22, 38); -- FINANCE_EXPORT_BTN -> FINANCE:EXPORT
```

## 5. 数据权限配置

### 5.1 创建数据权限规则

```sql
-- 创建数据权限规则
INSERT INTO `data_permission_rule` (
    `rule_code`, `rule_name`, `rule_type`, `resource_type`, 
    `filter_condition`, `description`, `status`
) VALUES (
    'FINANCE_DEPT_DATA', '财务部门数据', 'DEPT', 'FINANCE', 
    '{"department": "FINANCE"}', '只能查看财务部门的数据', 'ACTIVE'
);
```

### 5.2 为角色分配数据权限

```sql
-- 为角色分配数据权限
INSERT INTO `role_data_permission` (`role_id`, `data_permission_rule_id`) 
VALUES (8, 8);
```

## 6. 组织架构配置

### 6.1 创建组织

```sql
-- 创建组织
INSERT INTO `organization` (
    `org_code`, `org_name`, `org_type`, `parent_id`, `org_level`,
    `org_path`, `sort_order`, `description`, `status`
) VALUES (
    'DEPT_004', '财务部', 'DEPARTMENT', 1, 2,
    '/1/9/', 4, '财务管理部门', 'ACTIVE'
);
```

### 6.2 用户加入组织

```sql
-- 用户加入组织
INSERT INTO `user_organization` (
    `business_account_id`, `organization_id`, `position`, 
    `is_primary`, `join_date`, `status`
) VALUES (
    1001, 9, '财务经理', 1, '2025-01-27', 'ACTIVE'
);
```

## 7. 权限查询示例

### 7.1 查询用户权限

```sql
-- 查询用户的所有权限
SELECT DISTINCT p.permission_code, p.permission_name
FROM business_account_role bar
JOIN role_permission rp ON bar.role_id = rp.role_id
JOIN permission p ON rp.permission_id = p.id
WHERE bar.business_account_id = 1001 
  AND bar.status = 'ACTIVE' 
  AND bar.active = 1
  AND rp.grant_type = 'GRANT'
  AND rp.active = 1
  AND p.status = 'ACTIVE'
  AND p.active = 1;
```

### 7.2 查询用户菜单

```sql
-- 查询用户可访问的菜单
SELECT DISTINCT m.menu_code, m.menu_name, m.menu_type, 
       m.route_path, m.icon, m.sort_order
FROM business_account_role bar
JOIN role_permission rp ON bar.role_id = rp.role_id
JOIN menu_permission mp ON rp.permission_id = mp.permission_id
JOIN menu m ON mp.menu_id = m.id
WHERE bar.business_account_id = 1001 
  AND bar.status = 'ACTIVE' 
  AND bar.active = 1
  AND rp.grant_type = 'GRANT'
  AND rp.active = 1
  AND mp.active = 1
  AND m.status = 'ACTIVE'
  AND m.active = 1
ORDER BY m.sort_order;
```

### 7.3 查询用户数据权限

```sql
-- 查询用户的数据权限规则
SELECT DISTINCT dpr.rule_code, dpr.rule_name, dpr.rule_type, 
       dpr.resource_type, dpr.filter_condition
FROM business_account_role bar
JOIN role_data_permission rdp ON bar.role_id = rdp.role_id
JOIN data_permission_rule dpr ON rdp.data_permission_rule_id = dpr.id
WHERE bar.business_account_id = 1001 
  AND bar.status = 'ACTIVE' 
  AND bar.active = 1
  AND rdp.active = 1
  AND dpr.status = 'ACTIVE'
  AND dpr.active = 1;
```

## 8. 权限校验逻辑

### 8.1 API权限校验

```java
// 权限校验示例代码
public boolean hasPermission(Long userId, String permissionCode) {
    // 1. 查询用户的所有权限
    List<String> userPermissions = getUserPermissions(userId);
    
    // 2. 检查是否包含所需权限
    return userPermissions.contains(permissionCode);
}

// 获取用户权限列表
private List<String> getUserPermissions(Long userId) {
    String sql = """
        SELECT DISTINCT p.permission_code
        FROM business_account_role bar
        JOIN role_permission rp ON bar.role_id = rp.role_id
        JOIN permission p ON rp.permission_id = p.id
        WHERE bar.business_account_id = ? 
          AND bar.status = 'ACTIVE' 
          AND bar.active = 1
          AND rp.grant_type = 'GRANT'
          AND rp.active = 1
          AND p.status = 'ACTIVE'
          AND p.active = 1
        """;
    
    return jdbcTemplate.queryForList(sql, String.class, userId);
}
```

### 8.2 数据权限过滤

```java
// 数据权限过滤示例
public String buildDataPermissionFilter(Long userId, String resourceType) {
    // 1. 获取用户的数据权限规则
    List<DataPermissionRule> rules = getUserDataPermissionRules(userId, resourceType);
    
    // 2. 构建过滤条件
    StringBuilder filter = new StringBuilder();
    for (DataPermissionRule rule : rules) {
        if (filter.length() > 0) {
            filter.append(" OR ");
        }
        filter.append(parseFilterCondition(rule.getFilterCondition(), userId));
    }
    
    return filter.toString();
}
```

## 9. 最佳实践

### 9.1 权限设计原则

1. **最小权限原则**：用户只拥有完成工作所需的最小权限
2. **职责分离**：不同职责的用户拥有不同的权限集合
3. **权限继承**：下级组织可以继承上级组织的权限设置
4. **定期审查**：定期审查用户权限，及时回收不需要的权限

### 9.2 性能优化

1. **权限缓存**：使用Redis缓存用户权限信息
2. **批量查询**：使用IN查询批量获取权限信息
3. **索引优化**：为常用查询字段创建合适的索引
4. **分页查询**：对大数据量的权限查询使用分页

### 9.3 安全建议

1. **权限校验**：在每个API接口都要进行权限校验
2. **会话管理**：定期刷新用户权限缓存
3. **日志审计**：记录所有权限相关的操作日志
4. **定期清理**：定期清理过期的权限缓存和日志数据

## 10. 常见问题

### 10.1 用户无法访问某个菜单

**排查步骤**：
1. 检查用户是否分配了相应的角色
2. 检查角色是否分配了相应的权限
3. 检查菜单是否关联了相应的权限
4. 检查权限、角色、菜单的状态是否为ACTIVE

### 10.2 用户看不到某些数据

**排查步骤**：
1. 检查用户的数据权限规则
2. 检查数据权限规则的过滤条件
3. 检查用户的组织关系
4. 检查数据权限规则的状态

### 10.3 权限缓存不生效

**排查步骤**：
1. 检查Redis连接是否正常
2. 检查缓存键是否正确
3. 检查缓存过期时间设置
4. 检查权限变更后是否清理了缓存

## 11. 变更历史

| 版本 | 日期 | 作者 | 变更描述 | 关联功能/需求 |
|-----|------|------|---------|-------------|
| 1.0 | 2025-01-27 | 系统设计团队 | 初始版本 | 运营系统权限管理使用指南 |
