# 运营系统权限管理数据库设计

## 文档信息
- **版本**：1.0
- **作者**：系统设计团队
- **日期**：2025-01-27
- **状态**：草稿

## 1. 设计概述

本文档基于现有用户领域的表结构，设计运营系统的组织、权限、角色等相关表结构，支持细粒度的权限控制，包括菜单体系（菜单页面、页面按钮级别）和统一的权限命名规则（模块:动作）。

### 1.1 设计原则

1. **细粒度权限控制**：支持到菜单、页面、按钮级别的权限控制
2. **统一权限命名**：采用"模块:动作"的命名规则，如USER:READ、ORDER:EXPORT
3. **组织架构支持**：支持多层级组织架构和数据权限
4. **角色权限分离**：角色与权限分离，支持灵活的权限组合
5. **扩展性设计**：预留扩展字段，支持未来功能扩展

### 1.2 权限模型

采用RBAC（基于角色的访问控制）模型，结合组织架构和数据权限：

```
用户 -> 角色 -> 权限 -> 资源（菜单/功能）
用户 -> 组织 -> 数据权限范围
```

## 2. 组织架构相关表

### 2.1 组织表（organization）

```sql
CREATE TABLE `organization` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `org_code` varchar(50) NOT NULL COMMENT '组织编码，唯一',
  `org_name` varchar(100) NOT NULL COMMENT '组织名称',
  `org_type` varchar(20) NOT NULL COMMENT '组织类型：COMPANY-公司，DEPARTMENT-部门，TEAM-团队，GROUP-小组',
  `parent_id` bigint DEFAULT NULL COMMENT '父组织ID',
  `org_level` int NOT NULL DEFAULT '1' COMMENT '组织层级，从1开始',
  `org_path` varchar(500) DEFAULT NULL COMMENT '组织路径，如：/1/2/3/',
  `sort_order` int DEFAULT '0' COMMENT '排序顺序',
  `leader_id` bigint DEFAULT NULL COMMENT '负责人ID，关联business_account表',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `address` varchar(255) DEFAULT NULL COMMENT '办公地址',
  `description` varchar(500) DEFAULT NULL COMMENT '组织描述',
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-启用，INACTIVE-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_org_code` (`org_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_org_type` (`org_type`),
  KEY `idx_org_level` (`org_level`),
  KEY `idx_leader_id` (`leader_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='组织表';
```

### 2.2 用户组织关联表（user_organization）

```sql
CREATE TABLE `user_organization` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `business_account_id` bigint NOT NULL COMMENT '商户账户ID',
  `organization_id` bigint NOT NULL COMMENT '组织ID',
  `position` varchar(50) DEFAULT NULL COMMENT '职位',
  `is_primary` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否主要组织：0-否，1-是',
  `join_date` date DEFAULT NULL COMMENT '加入日期',
  `leave_date` date DEFAULT NULL COMMENT '离职日期',
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-在职，INACTIVE-离职',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_org_primary` (`business_account_id`, `is_primary`),
  KEY `idx_business_account_id` (`business_account_id`),
  KEY `idx_organization_id` (`organization_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户组织关联表';
```

## 3. 权限体系相关表

### 3.1 权限分组表（permission_group）

```sql
CREATE TABLE `permission_group` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_code` varchar(50) NOT NULL COMMENT '权限分组编码，唯一',
  `group_name` varchar(100) NOT NULL COMMENT '权限分组名称',
  `parent_id` bigint DEFAULT NULL COMMENT '父分组ID',
  `group_level` int NOT NULL DEFAULT '1' COMMENT '分组层级，从1开始',
  `sort_order` int DEFAULT '0' COMMENT '排序顺序',
  `icon` varchar(100) DEFAULT NULL COMMENT '图标',
  `description` varchar(500) DEFAULT NULL COMMENT '分组描述',
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-启用，INACTIVE-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_group_code` (`group_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_group_level` (`group_level`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限分组表';
```

### 3.2 权限表（permission）

```sql
CREATE TABLE `permission` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `permission_code` varchar(100) NOT NULL COMMENT '权限编码，格式：模块:动作，如USER:READ',
  `permission_name` varchar(100) NOT NULL COMMENT '权限名称',
  `permission_group_id` bigint DEFAULT NULL COMMENT '权限分组ID',
  `resource_type` varchar(20) NOT NULL COMMENT '资源类型：MENU-菜单，BUTTON-按钮，API-接口，DATA-数据',
  `resource_path` varchar(200) DEFAULT NULL COMMENT '资源路径，如菜单路径、API路径',
  `http_method` varchar(10) DEFAULT NULL COMMENT 'HTTP方法：GET、POST、PUT、DELETE等',
  `description` varchar(500) DEFAULT NULL COMMENT '权限描述',
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-启用，INACTIVE-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_permission_code` (`permission_code`),
  KEY `idx_permission_group_id` (`permission_group_id`),
  KEY `idx_resource_type` (`resource_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';
```

## 4. 菜单体系相关表

### 4.1 菜单表（menu）

```sql
CREATE TABLE `menu` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `menu_code` varchar(50) NOT NULL COMMENT '菜单编码，唯一',
  `menu_name` varchar(100) NOT NULL COMMENT '菜单名称',
  `menu_type` varchar(20) NOT NULL COMMENT '菜单类型：DIRECTORY-目录，MENU-菜单，BUTTON-按钮',
  `parent_id` bigint DEFAULT NULL COMMENT '父菜单ID',
  `menu_level` int NOT NULL DEFAULT '1' COMMENT '菜单层级，从1开始',
  `menu_path` varchar(500) DEFAULT NULL COMMENT '菜单路径，如：/1/2/3/',
  `route_path` varchar(200) DEFAULT NULL COMMENT '路由路径',
  `component_path` varchar(200) DEFAULT NULL COMMENT '组件路径',
  `icon` varchar(100) DEFAULT NULL COMMENT '图标',
  `sort_order` int DEFAULT '0' COMMENT '排序顺序',
  `is_visible` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否可见：0-隐藏，1-显示',
  `is_cache` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否缓存：0-不缓存，1-缓存',
  `is_external` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否外链：0-否，1-是',
  `external_url` varchar(500) DEFAULT NULL COMMENT '外链地址',
  `description` varchar(500) DEFAULT NULL COMMENT '菜单描述',
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-启用，INACTIVE-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_menu_code` (`menu_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_menu_type` (`menu_type`),
  KEY `idx_menu_level` (`menu_level`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜单表';
```

### 4.2 菜单权限关联表（menu_permission）

```sql
CREATE TABLE `menu_permission` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  `permission_id` bigint NOT NULL COMMENT '权限ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_menu_permission` (`menu_id`, `permission_id`),
  KEY `idx_menu_id` (`menu_id`),
  KEY `idx_permission_id` (`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜单权限关联表';
```

## 5. 角色权限相关表

### 5.1 角色权限关联表（role_permission）

```sql
CREATE TABLE `role_permission` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_id` bigint NOT NULL COMMENT '角色ID，关联role表',
  `permission_id` bigint NOT NULL COMMENT '权限ID',
  `grant_type` varchar(20) NOT NULL DEFAULT 'GRANT' COMMENT '授权类型：GRANT-授权，DENY-拒绝',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_permission_id` (`permission_id`),
  KEY `idx_grant_type` (`grant_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';
```

### 5.2 用户角色关联表（扩展现有的business_account_role）

现有的`business_account_role`表已经基本满足需求，建议增加以下字段：

```sql
-- 为现有的business_account_role表添加字段
ALTER TABLE `business_account_role`
ADD COLUMN `organization_id` bigint DEFAULT NULL COMMENT '组织ID，角色在特定组织内有效' AFTER `role_id`,
ADD COLUMN `effective_date` date DEFAULT NULL COMMENT '生效日期' AFTER `organization_id`,
ADD COLUMN `expiry_date` date DEFAULT NULL COMMENT '失效日期' AFTER `effective_date`,
ADD COLUMN `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-生效，INACTIVE-失效，EXPIRED-过期' AFTER `expiry_date`;

-- 添加索引
ALTER TABLE `business_account_role`
ADD KEY `idx_organization_id` (`organization_id`),
ADD KEY `idx_status` (`status`),
ADD KEY `idx_effective_date` (`effective_date`),
ADD KEY `idx_expiry_date` (`expiry_date`);
```

## 6. 数据权限相关表

### 6.1 数据权限规则表（data_permission_rule）

```sql
CREATE TABLE `data_permission_rule` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_code` varchar(50) NOT NULL COMMENT '规则编码，唯一',
  `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
  `rule_type` varchar(20) NOT NULL COMMENT '规则类型：SELF-本人，DEPT-本部门，DEPT_SUB-本部门及下级，ORG-本组织，ORG_SUB-本组织及下级，ALL-全部',
  `resource_type` varchar(50) NOT NULL COMMENT '资源类型，如：USER、ORDER、MERCHANT等',
  `filter_condition` text DEFAULT NULL COMMENT '过滤条件，JSON格式',
  `description` varchar(500) DEFAULT NULL COMMENT '规则描述',
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-启用，INACTIVE-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_rule_code` (`rule_code`),
  KEY `idx_rule_type` (`rule_type`),
  KEY `idx_resource_type` (`resource_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据权限规则表';
```

### 6.2 角色数据权限关联表（role_data_permission）

```sql
CREATE TABLE `role_data_permission` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_id` bigint NOT NULL COMMENT '角色ID，关联role表',
  `data_permission_rule_id` bigint NOT NULL COMMENT '数据权限规则ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_data_permission` (`role_id`, `data_permission_rule_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_data_permission_rule_id` (`data_permission_rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色数据权限关联表';
```

## 7. 权限缓存表（可选）

### 7.1 用户权限缓存表（user_permission_cache）

```sql
CREATE TABLE `user_permission_cache` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `business_account_id` bigint NOT NULL COMMENT '商户账户ID',
  `permission_codes` text NOT NULL COMMENT '权限编码列表，JSON格式',
  `menu_ids` text DEFAULT NULL COMMENT '菜单ID列表，JSON格式',
  `data_permission_rules` text DEFAULT NULL COMMENT '数据权限规则，JSON格式',
  `cache_version` varchar(32) NOT NULL COMMENT '缓存版本号',
  `expire_time` datetime NOT NULL COMMENT '过期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_business_account_id` (`business_account_id`),
  KEY `idx_expire_time` (`expire_time`),
  KEY `idx_cache_version` (`cache_version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户权限缓存表';
```

## 8. 权限操作日志表

### 8.1 权限操作日志表（permission_operation_log）

```sql
CREATE TABLE `permission_operation_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `operation_type` varchar(20) NOT NULL COMMENT '操作类型：GRANT-授权，REVOKE-撤销，LOGIN-登录，ACCESS-访问',
  `operator_id` bigint NOT NULL COMMENT '操作人ID',
  `target_user_id` bigint DEFAULT NULL COMMENT '目标用户ID',
  `target_role_id` bigint DEFAULT NULL COMMENT '目标角色ID',
  `permission_code` varchar(100) DEFAULT NULL COMMENT '权限编码',
  `resource_path` varchar(200) DEFAULT NULL COMMENT '访问资源路径',
  `operation_result` varchar(20) NOT NULL COMMENT '操作结果：SUCCESS-成功，FAILURE-失败',
  `failure_reason` varchar(500) DEFAULT NULL COMMENT '失败原因',
  `client_ip` varchar(50) DEFAULT NULL COMMENT '客户端IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `operation_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_target_user_id` (`target_user_id`),
  KEY `idx_target_role_id` (`target_role_id`),
  KEY `idx_operation_time` (`operation_time`),
  KEY `idx_operation_result` (`operation_result`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限操作日志表';
```

## 9. 权限示例数据

### 9.1 权限分组示例

```sql
-- 权限分组示例数据
INSERT INTO `permission_group` (`group_code`, `group_name`, `parent_id`, `group_level`, `sort_order`, `description`, `status`) VALUES
('SYSTEM', '系统管理', NULL, 1, 1, '系统管理相关权限', 'ACTIVE'),
('USER', '用户管理', 1, 2, 1, '用户管理相关权限', 'ACTIVE'),
('ROLE', '角色管理', 1, 2, 2, '角色管理相关权限', 'ACTIVE'),
('PERMISSION', '权限管理', 1, 2, 3, '权限管理相关权限', 'ACTIVE'),
('ORGANIZATION', '组织管理', 1, 2, 4, '组织管理相关权限', 'ACTIVE'),
('BUSINESS', '业务管理', NULL, 1, 2, '业务管理相关权限', 'ACTIVE'),
('MERCHANT', '商户管理', 6, 2, 1, '商户管理相关权限', 'ACTIVE'),
('ORDER', '订单管理', 6, 2, 2, '订单管理相关权限', 'ACTIVE'),
('PRODUCT', '商品管理', 6, 2, 3, '商品管理相关权限', 'ACTIVE'),
('REPORT', '报表管理', NULL, 1, 3, '报表管理相关权限', 'ACTIVE');
```

### 9.2 权限示例

```sql
-- 权限示例数据
INSERT INTO `permission` (`permission_code`, `permission_name`, `permission_group_id`, `resource_type`, `resource_path`, `http_method`, `description`, `status`) VALUES
-- 用户管理权限
('USER:READ', '查看用户', 2, 'API', '/api/users', 'GET', '查看用户列表和详情', 'ACTIVE'),
('USER:CREATE', '创建用户', 2, 'API', '/api/users', 'POST', '创建新用户', 'ACTIVE'),
('USER:UPDATE', '更新用户', 2, 'API', '/api/users/*', 'PUT', '更新用户信息', 'ACTIVE'),
('USER:DELETE', '删除用户', 2, 'API', '/api/users/*', 'DELETE', '删除用户', 'ACTIVE'),
('USER:EXPORT', '导出用户', 2, 'API', '/api/users/export', 'GET', '导出用户数据', 'ACTIVE'),
('USER:RESET_PASSWORD', '重置密码', 2, 'API', '/api/users/*/reset-password', 'POST', '重置用户密码', 'ACTIVE'),

-- 角色管理权限
('ROLE:READ', '查看角色', 3, 'API', '/api/roles', 'GET', '查看角色列表和详情', 'ACTIVE'),
('ROLE:CREATE', '创建角色', 3, 'API', '/api/roles', 'POST', '创建新角色', 'ACTIVE'),
('ROLE:UPDATE', '更新角色', 3, 'API', '/api/roles/*', 'PUT', '更新角色信息', 'ACTIVE'),
('ROLE:DELETE', '删除角色', 3, 'API', '/api/roles/*', 'DELETE', '删除角色', 'ACTIVE'),
('ROLE:ASSIGN', '分配角色', 3, 'API', '/api/roles/*/assign', 'POST', '为用户分配角色', 'ACTIVE'),

-- 权限管理权限
('PERMISSION:READ', '查看权限', 4, 'API', '/api/permissions', 'GET', '查看权限列表和详情', 'ACTIVE'),
('PERMISSION:CREATE', '创建权限', 4, 'API', '/api/permissions', 'POST', '创建新权限', 'ACTIVE'),
('PERMISSION:UPDATE', '更新权限', 4, 'API', '/api/permissions/*', 'PUT', '更新权限信息', 'ACTIVE'),
('PERMISSION:DELETE', '删除权限', 4, 'API', '/api/permissions/*', 'DELETE', '删除权限', 'ACTIVE'),

-- 组织管理权限
('ORGANIZATION:READ', '查看组织', 5, 'API', '/api/organizations', 'GET', '查看组织列表和详情', 'ACTIVE'),
('ORGANIZATION:CREATE', '创建组织', 5, 'API', '/api/organizations', 'POST', '创建新组织', 'ACTIVE'),
('ORGANIZATION:UPDATE', '更新组织', 5, 'API', '/api/organizations/*', 'PUT', '更新组织信息', 'ACTIVE'),
('ORGANIZATION:DELETE', '删除组织', 5, 'API', '/api/organizations/*', 'DELETE', '删除组织', 'ACTIVE'),

-- 商户管理权限
('MERCHANT:READ', '查看商户', 7, 'API', '/api/merchants', 'GET', '查看商户列表和详情', 'ACTIVE'),
('MERCHANT:CREATE', '创建商户', 7, 'API', '/api/merchants', 'POST', '创建新商户', 'ACTIVE'),
('MERCHANT:UPDATE', '更新商户', 7, 'API', '/api/merchants/*', 'PUT', '更新商户信息', 'ACTIVE'),
('MERCHANT:DELETE', '删除商户', 7, 'API', '/api/merchants/*', 'DELETE', '删除商户', 'ACTIVE'),
('MERCHANT:AUDIT', '审核商户', 7, 'API', '/api/merchants/*/audit', 'POST', '审核商户资质', 'ACTIVE'),
('MERCHANT:EXPORT', '导出商户', 7, 'API', '/api/merchants/export', 'GET', '导出商户数据', 'ACTIVE'),

-- 订单管理权限
('ORDER:READ', '查看订单', 8, 'API', '/api/orders', 'GET', '查看订单列表和详情', 'ACTIVE'),
('ORDER:UPDATE', '更新订单', 8, 'API', '/api/orders/*', 'PUT', '更新订单信息', 'ACTIVE'),
('ORDER:CANCEL', '取消订单', 8, 'API', '/api/orders/*/cancel', 'POST', '取消订单', 'ACTIVE'),
('ORDER:REFUND', '退款订单', 8, 'API', '/api/orders/*/refund', 'POST', '处理订单退款', 'ACTIVE'),
('ORDER:EXPORT', '导出订单', 8, 'API', '/api/orders/export', 'GET', '导出订单数据', 'ACTIVE'),

-- 商品管理权限
('PRODUCT:READ', '查看商品', 9, 'API', '/api/products', 'GET', '查看商品列表和详情', 'ACTIVE'),
('PRODUCT:CREATE', '创建商品', 9, 'API', '/api/products', 'POST', '创建新商品', 'ACTIVE'),
('PRODUCT:UPDATE', '更新商品', 9, 'API', '/api/products/*', 'PUT', '更新商品信息', 'ACTIVE'),
('PRODUCT:DELETE', '删除商品', 9, 'API', '/api/products/*', 'DELETE', '删除商品', 'ACTIVE'),
('PRODUCT:AUDIT', '审核商品', 9, 'API', '/api/products/*/audit', 'POST', '审核商品信息', 'ACTIVE'),

-- 报表管理权限
('REPORT:DASHBOARD', '查看仪表板', 10, 'MENU', '/dashboard', NULL, '查看运营仪表板', 'ACTIVE'),
('REPORT:USER_STATISTICS', '用户统计报表', 10, 'API', '/api/reports/user-statistics', 'GET', '查看用户统计报表', 'ACTIVE'),
('REPORT:ORDER_STATISTICS', '订单统计报表', 10, 'API', '/api/reports/order-statistics', 'GET', '查看订单统计报表', 'ACTIVE'),
('REPORT:MERCHANT_STATISTICS', '商户统计报表', 10, 'API', '/api/reports/merchant-statistics', 'GET', '查看商户统计报表', 'ACTIVE');
```

### 9.3 菜单示例

```sql
-- 菜单示例数据
INSERT INTO `menu` (`menu_code`, `menu_name`, `menu_type`, `parent_id`, `menu_level`, `route_path`, `component_path`, `icon`, `sort_order`, `description`, `status`) VALUES
-- 一级菜单
('DASHBOARD', '仪表板', 'MENU', NULL, 1, '/dashboard', 'Dashboard/Index', 'dashboard', 1, '运营仪表板', 'ACTIVE'),
('SYSTEM_MANAGEMENT', '系统管理', 'DIRECTORY', NULL, 1, '/system', NULL, 'system', 2, '系统管理目录', 'ACTIVE'),
('BUSINESS_MANAGEMENT', '业务管理', 'DIRECTORY', NULL, 1, '/business', NULL, 'business', 3, '业务管理目录', 'ACTIVE'),
('REPORT_MANAGEMENT', '报表管理', 'DIRECTORY', NULL, 1, '/reports', NULL, 'report', 4, '报表管理目录', 'ACTIVE'),

-- 系统管理二级菜单
('USER_MANAGEMENT', '用户管理', 'MENU', 2, 2, '/system/users', 'System/User/Index', 'user', 1, '用户管理页面', 'ACTIVE'),
('ROLE_MANAGEMENT', '角色管理', 'MENU', 2, 2, '/system/roles', 'System/Role/Index', 'role', 2, '角色管理页面', 'ACTIVE'),
('PERMISSION_MANAGEMENT', '权限管理', 'MENU', 2, 2, '/system/permissions', 'System/Permission/Index', 'permission', 3, '权限管理页面', 'ACTIVE'),
('ORGANIZATION_MANAGEMENT', '组织管理', 'MENU', 2, 2, '/system/organizations', 'System/Organization/Index', 'organization', 4, '组织管理页面', 'ACTIVE'),
('MENU_MANAGEMENT', '菜单管理', 'MENU', 2, 2, '/system/menus', 'System/Menu/Index', 'menu', 5, '菜单管理页面', 'ACTIVE'),

-- 业务管理二级菜单
('MERCHANT_MANAGEMENT', '商户管理', 'MENU', 3, 2, '/business/merchants', 'Business/Merchant/Index', 'merchant', 1, '商户管理页面', 'ACTIVE'),
('ORDER_MANAGEMENT', '订单管理', 'MENU', 3, 2, '/business/orders', 'Business/Order/Index', 'order', 2, '订单管理页面', 'ACTIVE'),
('PRODUCT_MANAGEMENT', '商品管理', 'MENU', 3, 2, '/business/products', 'Business/Product/Index', 'product', 3, '商品管理页面', 'ACTIVE'),

-- 报表管理二级菜单
('USER_REPORT', '用户报表', 'MENU', 4, 2, '/reports/users', 'Report/User/Index', 'user-report', 1, '用户统计报表', 'ACTIVE'),
('ORDER_REPORT', '订单报表', 'MENU', 4, 2, '/reports/orders', 'Report/Order/Index', 'order-report', 2, '订单统计报表', 'ACTIVE'),
('MERCHANT_REPORT', '商户报表', 'MENU', 4, 2, '/reports/merchants', 'Report/Merchant/Index', 'merchant-report', 3, '商户统计报表', 'ACTIVE'),

-- 按钮级权限
('USER_CREATE_BTN', '新增用户按钮', 'BUTTON', 5, 3, NULL, NULL, NULL, 1, '用户管理页面新增按钮', 'ACTIVE'),
('USER_EDIT_BTN', '编辑用户按钮', 'BUTTON', 5, 3, NULL, NULL, NULL, 2, '用户管理页面编辑按钮', 'ACTIVE'),
('USER_DELETE_BTN', '删除用户按钮', 'BUTTON', 5, 3, NULL, NULL, NULL, 3, '用户管理页面删除按钮', 'ACTIVE'),
('USER_EXPORT_BTN', '导出用户按钮', 'BUTTON', 5, 3, NULL, NULL, NULL, 4, '用户管理页面导出按钮', 'ACTIVE'),
('USER_RESET_PASSWORD_BTN', '重置密码按钮', 'BUTTON', 5, 3, NULL, NULL, NULL, 5, '用户管理页面重置密码按钮', 'ACTIVE');
```

### 9.4 菜单权限关联示例

```sql
-- 菜单权限关联示例数据
INSERT INTO `menu_permission` (`menu_id`, `permission_id`) VALUES
-- 仪表板权限
(1, 33), -- DASHBOARD -> REPORT:DASHBOARD

-- 用户管理页面权限
(5, 1),  -- USER_MANAGEMENT -> USER:READ
(5, 2),  -- USER_MANAGEMENT -> USER:CREATE
(5, 3),  -- USER_MANAGEMENT -> USER:UPDATE
(5, 4),  -- USER_MANAGEMENT -> USER:DELETE
(5, 5),  -- USER_MANAGEMENT -> USER:EXPORT
(5, 6),  -- USER_MANAGEMENT -> USER:RESET_PASSWORD

-- 用户管理按钮权限
(15, 2), -- USER_CREATE_BTN -> USER:CREATE
(16, 3), -- USER_EDIT_BTN -> USER:UPDATE
(17, 4), -- USER_DELETE_BTN -> USER:DELETE
(18, 5), -- USER_EXPORT_BTN -> USER:EXPORT
(19, 6), -- USER_RESET_PASSWORD_BTN -> USER:RESET_PASSWORD

-- 角色管理页面权限
(6, 7),  -- ROLE_MANAGEMENT -> ROLE:READ
(6, 8),  -- ROLE_MANAGEMENT -> ROLE:CREATE
(6, 9),  -- ROLE_MANAGEMENT -> ROLE:UPDATE
(6, 10), -- ROLE_MANAGEMENT -> ROLE:DELETE
(6, 11), -- ROLE_MANAGEMENT -> ROLE:ASSIGN

-- 商户管理页面权限
(10, 20), -- MERCHANT_MANAGEMENT -> MERCHANT:READ
(10, 21), -- MERCHANT_MANAGEMENT -> MERCHANT:CREATE
(10, 22), -- MERCHANT_MANAGEMENT -> MERCHANT:UPDATE
(10, 23), -- MERCHANT_MANAGEMENT -> MERCHANT:DELETE
(10, 24), -- MERCHANT_MANAGEMENT -> MERCHANT:AUDIT
(10, 25); -- MERCHANT_MANAGEMENT -> MERCHANT:EXPORT
```

### 9.5 角色示例

```sql
-- 角色示例数据
INSERT INTO `role` (`role_code`, `role_name`, `description`, `status`) VALUES
('SUPER_ADMIN', '超级管理员', '系统超级管理员，拥有所有权限', 'ACTIVE'),
('SYSTEM_ADMIN', '系统管理员', '系统管理员，负责用户、角色、权限管理', 'ACTIVE'),
('BUSINESS_ADMIN', '业务管理员', '业务管理员，负责商户、订单、商品管理', 'ACTIVE'),
('MERCHANT_MANAGER', '商户经理', '商户经理，负责商户管理和审核', 'ACTIVE'),
('ORDER_MANAGER', '订单经理', '订单经理，负责订单处理和客服', 'ACTIVE'),
('REPORT_ANALYST', '报表分析师', '报表分析师，负责数据分析和报表查看', 'ACTIVE'),
('CUSTOMER_SERVICE', '客服专员', '客服专员，负责客户服务和简单订单处理', 'ACTIVE');
```

### 9.6 数据权限规则示例

```sql
-- 数据权限规则示例数据
INSERT INTO `data_permission_rule` (`rule_code`, `rule_name`, `rule_type`, `resource_type`, `filter_condition`, `description`, `status`) VALUES
('SELF_DATA', '本人数据', 'SELF', 'ALL', '{"created_by": "${current_user_id}"}', '只能查看自己创建的数据', 'ACTIVE'),
('DEPT_DATA', '本部门数据', 'DEPT', 'ALL', '{"organization_id": "${current_user_org_id}"}', '只能查看本部门的数据', 'ACTIVE'),
('DEPT_SUB_DATA', '本部门及下级数据', 'DEPT_SUB', 'ALL', '{"organization_path": "LIKE ${current_user_org_path}%"}', '可以查看本部门及下级部门的数据', 'ACTIVE'),
('ORG_DATA', '本组织数据', 'ORG', 'ALL', '{"organization_id": "${current_user_root_org_id}"}', '只能查看本组织的数据', 'ACTIVE'),
('ALL_DATA', '全部数据', 'ALL', 'ALL', '{}', '可以查看所有数据', 'ACTIVE'),
('MERCHANT_REGION_DATA', '区域商户数据', 'CUSTOM', 'MERCHANT', '{"region_code": "${current_user_region_code}"}', '只能查看指定区域的商户数据', 'ACTIVE'),
('ORDER_RECENT_DATA', '近期订单数据', 'CUSTOM', 'ORDER', '{"created_at": ">= DATE_SUB(NOW(), INTERVAL 30 DAY)"}', '只能查看最近30天的订单数据', 'ACTIVE');
```

## 10. 使用说明

### 10.1 权限编码规范

权限编码采用"模块:动作"的格式，具体规范如下：

1. **模块名称**：使用大写英文，表示功能模块，如USER、ORDER、MERCHANT
2. **动作名称**：使用大写英文，表示具体操作，如READ、CREATE、UPDATE、DELETE
3. **分隔符**：使用冒号":"分隔模块和动作
4. **示例**：
   - `USER:READ` - 查看用户
   - `USER:CREATE` - 创建用户
   - `ORDER:EXPORT` - 导出订单
   - `MERCHANT:AUDIT` - 审核商户

### 10.2 菜单权限配置

1. **菜单类型**：
   - `DIRECTORY`：目录，用于组织菜单结构
   - `MENU`：菜单页面，对应具体的功能页面
   - `BUTTON`：按钮，页面内的操作按钮

2. **权限关联**：
   - 每个菜单可以关联多个权限
   - 用户必须拥有菜单关联的所有权限才能访问该菜单
   - 按钮级权限可以控制页面内具体操作的显示

### 10.3 数据权限配置

1. **规则类型**：
   - `SELF`：本人数据
   - `DEPT`：本部门数据
   - `DEPT_SUB`：本部门及下级数据
   - `ORG`：本组织数据
   - `ORG_SUB`：本组织及下级数据
   - `ALL`：全部数据
   - `CUSTOM`：自定义规则

2. **过滤条件**：
   - 使用JSON格式定义过滤条件
   - 支持变量替换，如`${current_user_id}`、`${current_user_org_id}`
   - 支持SQL函数和操作符

### 10.4 角色权限分配

1. **角色创建**：在`role`表中创建角色
2. **权限分配**：在`role_permission`表中为角色分配权限
3. **用户授权**：在`business_account_role`表中为用户分配角色
4. **数据权限**：在`role_data_permission`表中为角色分配数据权限规则

## 11. 最佳实践

### 11.1 权限设计原则

1. **最小权限原则**：用户只拥有完成工作所需的最小权限
2. **职责分离**：不同职责的用户拥有不同的权限集合
3. **权限继承**：下级组织可以继承上级组织的权限设置
4. **审计跟踪**：所有权限操作都要记录日志

### 11.2 性能优化建议

1. **权限缓存**：使用Redis缓存用户权限信息，减少数据库查询
2. **批量查询**：使用IN查询批量获取权限信息
3. **索引优化**：为常用查询字段创建合适的索引
4. **分页查询**：对大数据量的权限查询使用分页

### 11.3 安全建议

1. **权限校验**：在每个API接口都要进行权限校验
2. **会话管理**：定期刷新用户权限缓存
3. **日志审计**：记录所有权限相关的操作日志
4. **定期清理**：定期清理过期的权限缓存和日志数据

## 12. 变更历史

| 版本 | 日期 | 作者 | 变更描述 | 关联功能/需求 |
|-----|------|------|---------|-------------|
| 1.0 | 2025-01-27 | 系统设计团队 | 初始版本 | 运营系统权限管理设计 |
