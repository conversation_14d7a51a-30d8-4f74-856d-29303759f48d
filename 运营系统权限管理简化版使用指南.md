# 运营系统权限管理简化版使用指南

## 文档信息
- **版本**：2.0
- **作者**：系统设计团队
- **日期**：2025-01-27
- **状态**：草稿

## 1. 设计概述

基于您修改后的SQL结构，本系统采用简化的权限管理设计：

### 1.1 核心表结构

1. **organization** - 组织表，支持树形组织架构
2. **permission** - 权限表，统一管理菜单、页面、按钮、API权限
3. **role** - 角色表，支持组织归属
4. **role_permission** - 角色权限关联表
5. **business_account_role** - 用户角色关联表
6. **permission_operation_log** - 权限操作日志表

### 1.2 权限类型

通过`permission`表的`type`字段区分四种权限类型：

- **MENU** - 菜单权限（一级导航菜单）
- **PAGE** - 页面权限（具体功能页面）
- **BUTTON** - 按钮权限（页面内操作按钮）
- **API** - 接口权限（后端API接口）

### 1.3 权限编码规范

支持两种权限编码方式：

1. **功能编码**：`MENU_DASHBOARD`、`PAGE_USER_MANAGEMENT`、`BUTTON_USER_CREATE`
2. **模块:动作**：`USER:READ`、`ORDER:CREATE`、`MERCHANT:AUDIT`

## 2. 权限树形结构

通过`parent_id`字段构建权限的层级关系：

```
MENU_SYSTEM (系统管理菜单)
├── PAGE_USER_MANAGEMENT (用户管理页面)
│   ├── BUTTON_USER_CREATE (新增用户按钮)
│   ├── BUTTON_USER_EDIT (编辑用户按钮)
│   ├── USER:READ (查看用户API)
│   ├── USER:CREATE (创建用户API)
│   └── USER:UPDATE (更新用户API)
└── PAGE_ROLE_MANAGEMENT (角色管理页面)
    ├── BUTTON_ROLE_CREATE (新增角色按钮)
    ├── ROLE:READ (查看角色API)
    └── ROLE:CREATE (创建角色API)
```

## 3. 常用查询示例

### 3.1 查询用户菜单树

```sql
-- 查询用户可访问的菜单树
WITH RECURSIVE user_permissions AS (
    SELECT DISTINCT rp.permission_id
    FROM business_account_role bar
    JOIN role_permission rp ON bar.role_id = rp.role_id
    WHERE bar.business_account_id = ? 
      AND bar.status = 'ACTIVE' 
      AND bar.active = 1
      AND rp.grant_type = 'GRANT'
      AND rp.active = 1
),
menu_tree AS (
    -- 根菜单
    SELECT p.id, p.permission_code, p.permission_name, p.type, 
           p.parent_id, p.url, p.icon, p.order_num, 0 as level
    FROM permission p
    JOIN user_permissions up ON p.id = up.permission_id
    WHERE p.parent_id IS NULL 
      AND p.type = 'MENU'
      AND p.active = 1
    
    UNION ALL
    
    -- 子菜单
    SELECT p.id, p.permission_code, p.permission_name, p.type,
           p.parent_id, p.url, p.icon, p.order_num, mt.level + 1
    FROM permission p
    JOIN menu_tree mt ON p.parent_id = mt.id
    JOIN user_permissions up ON p.id = up.permission_id
    WHERE p.type IN ('MENU', 'PAGE')
      AND p.active = 1
)
SELECT * FROM menu_tree ORDER BY level, order_num;
```

### 3.2 查询用户按钮权限

```sql
-- 查询用户在特定页面的按钮权限
SELECT p.permission_code, p.permission_name
FROM business_account_role bar
JOIN role_permission rp ON bar.role_id = rp.role_id
JOIN permission p ON rp.permission_id = p.id
WHERE bar.business_account_id = ?
  AND bar.status = 'ACTIVE'
  AND bar.active = 1
  AND rp.grant_type = 'GRANT'
  AND rp.active = 1
  AND p.type = 'BUTTON'
  AND p.parent_id = (
      SELECT id FROM permission 
      WHERE permission_code = 'PAGE_USER_MANAGEMENT'
  )
  AND p.active = 1;
```

### 3.3 查询用户API权限

```sql
-- 检查用户是否有特定API权限
SELECT COUNT(1) > 0 as has_permission
FROM business_account_role bar
JOIN role_permission rp ON bar.role_id = rp.role_id
JOIN permission p ON rp.permission_id = p.id
WHERE bar.business_account_id = ?
  AND bar.status = 'ACTIVE'
  AND bar.active = 1
  AND rp.grant_type = 'GRANT'
  AND rp.active = 1
  AND p.permission_code = ?
  AND p.type = 'API'
  AND p.active = 1;
```

## 4. 权限配置操作

### 4.1 创建权限

```sql
-- 创建菜单权限
INSERT INTO `permission` (
    `permission_code`, `permission_name`, `type`, `parent_id`, 
    `url`, `icon`, `order_num`, `description`
) VALUES (
    'MENU_FINANCE', '财务管理', 'MENU', NULL, 
    '/finance', 'finance', 5, '财务管理菜单'
);

-- 创建页面权限
INSERT INTO `permission` (
    `permission_code`, `permission_name`, `type`, `parent_id`, 
    `url`, `component_path`, `order_num`, `description`
) VALUES (
    'PAGE_FINANCE_REPORT', '财务报表', 'PAGE', 
    (SELECT id FROM permission WHERE permission_code = 'MENU_FINANCE'), 
    '/finance/reports', 'Finance/Report/Index', 1, '财务报表页面'
);

-- 创建按钮权限
INSERT INTO `permission` (
    `permission_code`, `permission_name`, `type`, `parent_id`, 
    `order_num`, `description`
) VALUES (
    'BUTTON_FINANCE_EXPORT', '导出财务数据', 'BUTTON', 
    (SELECT id FROM permission WHERE permission_code = 'PAGE_FINANCE_REPORT'), 
    1, '财务报表导出按钮'
);

-- 创建API权限
INSERT INTO `permission` (
    `permission_code`, `permission_name`, `type`, `parent_id`, 
    `url`, `http_method`, `order_num`, `description`
) VALUES (
    'FINANCE:READ', '查看财务数据', 'API', 
    (SELECT id FROM permission WHERE permission_code = 'PAGE_FINANCE_REPORT'), 
    '/api/finance/reports', 'GET', 1, '查看财务报表API'
);
```

### 4.2 角色权限分配

```sql
-- 创建角色
INSERT INTO `role` (`role_code`, `role_name`, `org_id`, `description`) 
VALUES ('FINANCE_MANAGER', '财务经理', 5, '财务部门经理角色');

-- 为角色分配权限
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`) 
VALUES (
    (SELECT id FROM role WHERE role_code = 'FINANCE_MANAGER'),
    (SELECT id FROM permission WHERE permission_code = 'MENU_FINANCE'),
    'GRANT'
);

-- 为用户分配角色
INSERT INTO `business_account_role` (
    `business_account_id`, `role_id`, `org_id`, 
    `effective_date`, `expiry_date`, `status`
) VALUES (
    1001, 
    (SELECT id FROM role WHERE role_code = 'FINANCE_MANAGER'),
    5, '2025-01-27', '2025-12-31', 'ACTIVE'
);
```

## 5. 权限校验实现

### 5.1 Java代码示例

```java
@Service
public class PermissionService {
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    // 检查菜单权限
    public boolean hasMenuPermission(Long userId, String menuCode) {
        String sql = """
            SELECT COUNT(1) > 0
            FROM business_account_role bar
            JOIN role_permission rp ON bar.role_id = rp.role_id
            JOIN permission p ON rp.permission_id = p.id
            WHERE bar.business_account_id = ?
              AND bar.status = 'ACTIVE'
              AND bar.active = 1
              AND rp.grant_type = 'GRANT'
              AND rp.active = 1
              AND p.permission_code = ?
              AND p.type IN ('MENU', 'PAGE')
              AND p.active = 1
            """;
        
        return jdbcTemplate.queryForObject(sql, Boolean.class, userId, menuCode);
    }
    
    // 检查按钮权限
    public boolean hasButtonPermission(Long userId, String buttonCode) {
        String sql = """
            SELECT COUNT(1) > 0
            FROM business_account_role bar
            JOIN role_permission rp ON bar.role_id = rp.role_id
            JOIN permission p ON rp.permission_id = p.id
            WHERE bar.business_account_id = ?
              AND bar.status = 'ACTIVE'
              AND bar.active = 1
              AND rp.grant_type = 'GRANT'
              AND rp.active = 1
              AND p.permission_code = ?
              AND p.type = 'BUTTON'
              AND p.active = 1
            """;
        
        return jdbcTemplate.queryForObject(sql, Boolean.class, userId, buttonCode);
    }
    
    // 检查API权限
    public boolean hasApiPermission(Long userId, String permissionCode) {
        String sql = """
            SELECT COUNT(1) > 0
            FROM business_account_role bar
            JOIN role_permission rp ON bar.role_id = rp.role_id
            JOIN permission p ON rp.permission_id = p.id
            WHERE bar.business_account_id = ?
              AND bar.status = 'ACTIVE'
              AND bar.active = 1
              AND rp.grant_type = 'GRANT'
              AND rp.active = 1
              AND p.permission_code = ?
              AND p.type = 'API'
              AND p.active = 1
            """;
        
        return jdbcTemplate.queryForObject(sql, Boolean.class, userId, permissionCode);
    }
    
    // 获取用户菜单树
    public List<MenuTreeNode> getUserMenuTree(Long userId) {
        // 实现菜单树查询逻辑
        // 返回用户可访问的菜单树结构
    }
}
```

### 5.2 权限注解

```java
// 自定义权限校验注解
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface RequirePermission {
    String value(); // 权限编码
}

// 使用示例
@RestController
@RequestMapping("/api/users")
public class UserController {
    
    @GetMapping
    @RequirePermission("USER:READ")
    public List<User> getUsers() {
        // 查看用户列表
    }
    
    @PostMapping
    @RequirePermission("USER:CREATE")
    public User createUser(@RequestBody User user) {
        // 创建用户
    }
}
```

## 6. 最佳实践

### 6.1 权限设计原则

1. **层次清晰**：菜单 -> 页面 -> 按钮 -> API 的层次结构
2. **粒度适中**：既要细粒度控制，又要避免过度复杂
3. **易于维护**：权限编码要有规律，便于管理
4. **最小权限**：用户只拥有必需的权限

### 6.2 性能优化

1. **权限缓存**：使用Redis缓存用户权限信息
2. **批量查询**：使用IN查询批量获取权限信息
3. **索引优化**：为常用查询字段创建合适的索引

### 6.3 安全建议

1. **权限校验**：在每个API接口都要进行权限校验
2. **定期审查**：定期检查用户权限分配
3. **操作日志**：记录所有权限相关操作
4. **会话管理**：定期刷新用户权限缓存

## 7. 变更历史

| 版本 | 日期 | 作者 | 变更描述 | 关联功能/需求 |
|-----|------|------|---------|-------------|
| 2.0 | 2025-01-27 | 系统设计团队 | 基于用户修改后的简化版本 | 运营系统权限管理简化版 |
| 1.0 | 2025-01-27 | 系统设计团队 | 初始版本 | 运营系统权限管理优化版本 |
