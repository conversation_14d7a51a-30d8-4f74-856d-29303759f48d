-- 运营系统权限管理示例数据脚本
-- 版本：1.0
-- 作者：系统设计团队
-- 日期：2025-01-27

-- =============================================
-- 1. 权限分组示例数据
-- =============================================

INSERT INTO `permission_group` (`group_code`, `group_name`, `parent_id`, `group_level`, `sort_order`, `description`, `status`) VALUES
('SYSTEM', '系统管理', NULL, 1, 1, '系统管理相关权限', 'ACTIVE'),
('USER', '用户管理', 1, 2, 1, '用户管理相关权限', 'ACTIVE'),
('ROLE', '角色管理', 1, 2, 2, '角色管理相关权限', 'ACTIVE'),
('PERMISSION', '权限管理', 1, 2, 3, '权限管理相关权限', 'ACTIVE'),
('ORGANIZATION', '组织管理', 1, 2, 4, '组织管理相关权限', 'ACTIVE'),
('BUSINESS', '业务管理', NULL, 1, 2, '业务管理相关权限', 'ACTIVE'),
('MERCHANT', '商户管理', 6, 2, 1, '商户管理相关权限', 'ACTIVE'),
('ORDER', '订单管理', 6, 2, 2, '订单管理相关权限', 'ACTIVE'),
('PRODUCT', '商品管理', 6, 2, 3, '商品管理相关权限', 'ACTIVE'),
('REPORT', '报表管理', NULL, 1, 3, '报表管理相关权限', 'ACTIVE');

-- =============================================
-- 2. 权限示例数据
-- =============================================

INSERT INTO `permission` (`permission_code`, `permission_name`, `permission_group_id`, `resource_type`, `resource_path`, `http_method`, `description`, `status`) VALUES
-- 用户管理权限
('USER:READ', '查看用户', 2, 'API', '/api/users', 'GET', '查看用户列表和详情', 'ACTIVE'),
('USER:CREATE', '创建用户', 2, 'API', '/api/users', 'POST', '创建新用户', 'ACTIVE'),
('USER:UPDATE', '更新用户', 2, 'API', '/api/users/*', 'PUT', '更新用户信息', 'ACTIVE'),
('USER:DELETE', '删除用户', 2, 'API', '/api/users/*', 'DELETE', '删除用户', 'ACTIVE'),
('USER:EXPORT', '导出用户', 2, 'API', '/api/users/export', 'GET', '导出用户数据', 'ACTIVE'),
('USER:RESET_PASSWORD', '重置密码', 2, 'API', '/api/users/*/reset-password', 'POST', '重置用户密码', 'ACTIVE'),

-- 角色管理权限
('ROLE:READ', '查看角色', 3, 'API', '/api/roles', 'GET', '查看角色列表和详情', 'ACTIVE'),
('ROLE:CREATE', '创建角色', 3, 'API', '/api/roles', 'POST', '创建新角色', 'ACTIVE'),
('ROLE:UPDATE', '更新角色', 3, 'API', '/api/roles/*', 'PUT', '更新角色信息', 'ACTIVE'),
('ROLE:DELETE', '删除角色', 3, 'API', '/api/roles/*', 'DELETE', '删除角色', 'ACTIVE'),
('ROLE:ASSIGN', '分配角色', 3, 'API', '/api/roles/*/assign', 'POST', '为用户分配角色', 'ACTIVE'),

-- 权限管理权限
('PERMISSION:READ', '查看权限', 4, 'API', '/api/permissions', 'GET', '查看权限列表和详情', 'ACTIVE'),
('PERMISSION:CREATE', '创建权限', 4, 'API', '/api/permissions', 'POST', '创建新权限', 'ACTIVE'),
('PERMISSION:UPDATE', '更新权限', 4, 'API', '/api/permissions/*', 'PUT', '更新权限信息', 'ACTIVE'),
('PERMISSION:DELETE', '删除权限', 4, 'API', '/api/permissions/*', 'DELETE', '删除权限', 'ACTIVE'),

-- 组织管理权限
('ORGANIZATION:READ', '查看组织', 5, 'API', '/api/organizations', 'GET', '查看组织列表和详情', 'ACTIVE'),
('ORGANIZATION:CREATE', '创建组织', 5, 'API', '/api/organizations', 'POST', '创建新组织', 'ACTIVE'),
('ORGANIZATION:UPDATE', '更新组织', 5, 'API', '/api/organizations/*', 'PUT', '更新组织信息', 'ACTIVE'),
('ORGANIZATION:DELETE', '删除组织', 5, 'API', '/api/organizations/*', 'DELETE', '删除组织', 'ACTIVE'),

-- 商户管理权限
('MERCHANT:READ', '查看商户', 7, 'API', '/api/merchants', 'GET', '查看商户列表和详情', 'ACTIVE'),
('MERCHANT:CREATE', '创建商户', 7, 'API', '/api/merchants', 'POST', '创建新商户', 'ACTIVE'),
('MERCHANT:UPDATE', '更新商户', 7, 'API', '/api/merchants/*', 'PUT', '更新商户信息', 'ACTIVE'),
('MERCHANT:DELETE', '删除商户', 7, 'API', '/api/merchants/*', 'DELETE', '删除商户', 'ACTIVE'),
('MERCHANT:AUDIT', '审核商户', 7, 'API', '/api/merchants/*/audit', 'POST', '审核商户资质', 'ACTIVE'),
('MERCHANT:EXPORT', '导出商户', 7, 'API', '/api/merchants/export', 'GET', '导出商户数据', 'ACTIVE'),

-- 订单管理权限
('ORDER:READ', '查看订单', 8, 'API', '/api/orders', 'GET', '查看订单列表和详情', 'ACTIVE'),
('ORDER:UPDATE', '更新订单', 8, 'API', '/api/orders/*', 'PUT', '更新订单信息', 'ACTIVE'),
('ORDER:CANCEL', '取消订单', 8, 'API', '/api/orders/*/cancel', 'POST', '取消订单', 'ACTIVE'),
('ORDER:REFUND', '退款订单', 8, 'API', '/api/orders/*/refund', 'POST', '处理订单退款', 'ACTIVE'),
('ORDER:EXPORT', '导出订单', 8, 'API', '/api/orders/export', 'GET', '导出订单数据', 'ACTIVE'),

-- 商品管理权限
('PRODUCT:READ', '查看商品', 9, 'API', '/api/products', 'GET', '查看商品列表和详情', 'ACTIVE'),
('PRODUCT:CREATE', '创建商品', 9, 'API', '/api/products', 'POST', '创建新商品', 'ACTIVE'),
('PRODUCT:UPDATE', '更新商品', 9, 'API', '/api/products/*', 'PUT', '更新商品信息', 'ACTIVE'),
('PRODUCT:DELETE', '删除商品', 9, 'API', '/api/products/*', 'DELETE', '删除商品', 'ACTIVE'),
('PRODUCT:AUDIT', '审核商品', 9, 'API', '/api/products/*/audit', 'POST', '审核商品信息', 'ACTIVE'),

-- 报表管理权限
('REPORT:DASHBOARD', '查看仪表板', 10, 'MENU', '/dashboard', NULL, '查看运营仪表板', 'ACTIVE'),
('REPORT:USER_STATISTICS', '用户统计报表', 10, 'API', '/api/reports/user-statistics', 'GET', '查看用户统计报表', 'ACTIVE'),
('REPORT:ORDER_STATISTICS', '订单统计报表', 10, 'API', '/api/reports/order-statistics', 'GET', '查看订单统计报表', 'ACTIVE'),
('REPORT:MERCHANT_STATISTICS', '商户统计报表', 10, 'API', '/api/reports/merchant-statistics', 'GET', '查看商户统计报表', 'ACTIVE');

-- =============================================
-- 3. 菜单示例数据
-- =============================================

INSERT INTO `menu` (`menu_code`, `menu_name`, `menu_type`, `parent_id`, `menu_level`, `route_path`, `component_path`, `icon`, `sort_order`, `description`, `status`) VALUES
-- 一级菜单
('DASHBOARD', '仪表板', 'MENU', NULL, 1, '/dashboard', 'Dashboard/Index', 'dashboard', 1, '运营仪表板', 'ACTIVE'),
('SYSTEM_MANAGEMENT', '系统管理', 'DIRECTORY', NULL, 1, '/system', NULL, 'system', 2, '系统管理目录', 'ACTIVE'),
('BUSINESS_MANAGEMENT', '业务管理', 'DIRECTORY', NULL, 1, '/business', NULL, 'business', 3, '业务管理目录', 'ACTIVE'),
('REPORT_MANAGEMENT', '报表管理', 'DIRECTORY', NULL, 1, '/reports', NULL, 'report', 4, '报表管理目录', 'ACTIVE'),

-- 系统管理二级菜单
('USER_MANAGEMENT', '用户管理', 'MENU', 2, 2, '/system/users', 'System/User/Index', 'user', 1, '用户管理页面', 'ACTIVE'),
('ROLE_MANAGEMENT', '角色管理', 'MENU', 2, 2, '/system/roles', 'System/Role/Index', 'role', 2, '角色管理页面', 'ACTIVE'),
('PERMISSION_MANAGEMENT', '权限管理', 'MENU', 2, 2, '/system/permissions', 'System/Permission/Index', 'permission', 3, '权限管理页面', 'ACTIVE'),
('ORGANIZATION_MANAGEMENT', '组织管理', 'MENU', 2, 2, '/system/organizations', 'System/Organization/Index', 'organization', 4, '组织管理页面', 'ACTIVE'),
('MENU_MANAGEMENT', '菜单管理', 'MENU', 2, 2, '/system/menus', 'System/Menu/Index', 'menu', 5, '菜单管理页面', 'ACTIVE'),

-- 业务管理二级菜单
('MERCHANT_MANAGEMENT', '商户管理', 'MENU', 3, 2, '/business/merchants', 'Business/Merchant/Index', 'merchant', 1, '商户管理页面', 'ACTIVE'),
('ORDER_MANAGEMENT', '订单管理', 'MENU', 3, 2, '/business/orders', 'Business/Order/Index', 'order', 2, '订单管理页面', 'ACTIVE'),
('PRODUCT_MANAGEMENT', '商品管理', 'MENU', 3, 2, '/business/products', 'Business/Product/Index', 'product', 3, '商品管理页面', 'ACTIVE'),

-- 报表管理二级菜单
('USER_REPORT', '用户报表', 'MENU', 4, 2, '/reports/users', 'Report/User/Index', 'user-report', 1, '用户统计报表', 'ACTIVE'),
('ORDER_REPORT', '订单报表', 'MENU', 4, 2, '/reports/orders', 'Report/Order/Index', 'order-report', 2, '订单统计报表', 'ACTIVE'),
('MERCHANT_REPORT', '商户报表', 'MENU', 4, 2, '/reports/merchants', 'Report/Merchant/Index', 'merchant-report', 3, '商户统计报表', 'ACTIVE'),

-- 按钮级权限
('USER_CREATE_BTN', '新增用户按钮', 'BUTTON', 5, 3, NULL, NULL, NULL, 1, '用户管理页面新增按钮', 'ACTIVE'),
('USER_EDIT_BTN', '编辑用户按钮', 'BUTTON', 5, 3, NULL, NULL, NULL, 2, '用户管理页面编辑按钮', 'ACTIVE'),
('USER_DELETE_BTN', '删除用户按钮', 'BUTTON', 5, 3, NULL, NULL, NULL, 3, '用户管理页面删除按钮', 'ACTIVE'),
('USER_EXPORT_BTN', '导出用户按钮', 'BUTTON', 5, 3, NULL, NULL, NULL, 4, '用户管理页面导出按钮', 'ACTIVE'),
('USER_RESET_PASSWORD_BTN', '重置密码按钮', 'BUTTON', 5, 3, NULL, NULL, NULL, 5, '用户管理页面重置密码按钮', 'ACTIVE');

-- =============================================
-- 4. 菜单权限关联示例数据
-- =============================================

INSERT INTO `menu_permission` (`menu_id`, `permission_id`) VALUES
-- 仪表板权限
(1, 33), -- DASHBOARD -> REPORT:DASHBOARD

-- 用户管理页面权限
(5, 1),  -- USER_MANAGEMENT -> USER:READ
(5, 2),  -- USER_MANAGEMENT -> USER:CREATE
(5, 3),  -- USER_MANAGEMENT -> USER:UPDATE
(5, 4),  -- USER_MANAGEMENT -> USER:DELETE
(5, 5),  -- USER_MANAGEMENT -> USER:EXPORT
(5, 6),  -- USER_MANAGEMENT -> USER:RESET_PASSWORD

-- 用户管理按钮权限
(15, 2), -- USER_CREATE_BTN -> USER:CREATE
(16, 3), -- USER_EDIT_BTN -> USER:UPDATE
(17, 4), -- USER_DELETE_BTN -> USER:DELETE
(18, 5), -- USER_EXPORT_BTN -> USER:EXPORT
(19, 6), -- USER_RESET_PASSWORD_BTN -> USER:RESET_PASSWORD

-- 角色管理页面权限
(6, 7),  -- ROLE_MANAGEMENT -> ROLE:READ
(6, 8),  -- ROLE_MANAGEMENT -> ROLE:CREATE
(6, 9),  -- ROLE_MANAGEMENT -> ROLE:UPDATE
(6, 10), -- ROLE_MANAGEMENT -> ROLE:DELETE
(6, 11), -- ROLE_MANAGEMENT -> ROLE:ASSIGN

-- 商户管理页面权限
(10, 20), -- MERCHANT_MANAGEMENT -> MERCHANT:READ
(10, 21), -- MERCHANT_MANAGEMENT -> MERCHANT:CREATE
(10, 22), -- MERCHANT_MANAGEMENT -> MERCHANT:UPDATE
(10, 23), -- MERCHANT_MANAGEMENT -> MERCHANT:DELETE
(10, 24), -- MERCHANT_MANAGEMENT -> MERCHANT:AUDIT
(10, 25); -- MERCHANT_MANAGEMENT -> MERCHANT:EXPORT

-- =============================================
-- 5. 角色示例数据
-- =============================================

INSERT INTO `role` (`role_code`, `role_name`, `description`, `status`) VALUES
('SUPER_ADMIN', '超级管理员', '系统超级管理员，拥有所有权限', 'ACTIVE'),
('SYSTEM_ADMIN', '系统管理员', '系统管理员，负责用户、角色、权限管理', 'ACTIVE'),
('BUSINESS_ADMIN', '业务管理员', '业务管理员，负责商户、订单、商品管理', 'ACTIVE'),
('MERCHANT_MANAGER', '商户经理', '商户经理，负责商户管理和审核', 'ACTIVE'),
('ORDER_MANAGER', '订单经理', '订单经理，负责订单处理和客服', 'ACTIVE'),
('REPORT_ANALYST', '报表分析师', '报表分析师，负责数据分析和报表查看', 'ACTIVE'),
('CUSTOMER_SERVICE', '客服专员', '客服专员，负责客户服务和简单订单处理', 'ACTIVE');

-- =============================================
-- 6. 数据权限规则示例数据
-- =============================================

INSERT INTO `data_permission_rule` (`rule_code`, `rule_name`, `rule_type`, `resource_type`, `filter_condition`, `description`, `status`) VALUES
('SELF_DATA', '本人数据', 'SELF', 'ALL', '{"created_by": "${current_user_id}"}', '只能查看自己创建的数据', 'ACTIVE'),
('DEPT_DATA', '本部门数据', 'DEPT', 'ALL', '{"organization_id": "${current_user_org_id}"}', '只能查看本部门的数据', 'ACTIVE'),
('DEPT_SUB_DATA', '本部门及下级数据', 'DEPT_SUB', 'ALL', '{"organization_path": "LIKE ${current_user_org_path}%"}', '可以查看本部门及下级部门的数据', 'ACTIVE'),
('ORG_DATA', '本组织数据', 'ORG', 'ALL', '{"organization_id": "${current_user_root_org_id}"}', '只能查看本组织的数据', 'ACTIVE'),
('ALL_DATA', '全部数据', 'ALL', 'ALL', '{}', '可以查看所有数据', 'ACTIVE'),
('MERCHANT_REGION_DATA', '区域商户数据', 'CUSTOM', 'MERCHANT', '{"region_code": "${current_user_region_code}"}', '只能查看指定区域的商户数据', 'ACTIVE'),
('ORDER_RECENT_DATA', '近期订单数据', 'CUSTOM', 'ORDER', '{"created_at": ">= DATE_SUB(NOW(), INTERVAL 30 DAY)"}', '只能查看最近30天的订单数据', 'ACTIVE');

-- =============================================
-- 7. 组织示例数据
-- =============================================

INSERT INTO `organization` (`org_code`, `org_name`, `org_type`, `parent_id`, `org_level`, `org_path`, `sort_order`, `description`, `status`) VALUES
('COMPANY_001', '老猫点评科技有限公司', 'COMPANY', NULL, 1, '/1/', 1, '公司总部', 'ACTIVE'),
('DEPT_001', '技术部', 'DEPARTMENT', 1, 2, '/1/2/', 1, '技术研发部门', 'ACTIVE'),
('DEPT_002', '运营部', 'DEPARTMENT', 1, 2, '/1/3/', 2, '运营管理部门', 'ACTIVE'),
('DEPT_003', '市场部', 'DEPARTMENT', 1, 2, '/1/4/', 3, '市场营销部门', 'ACTIVE'),
('TEAM_001', '后端开发团队', 'TEAM', 2, 3, '/1/2/5/', 1, '后端开发团队', 'ACTIVE'),
('TEAM_002', '前端开发团队', 'TEAM', 2, 3, '/1/2/6/', 2, '前端开发团队', 'ACTIVE'),
('TEAM_003', '商户运营团队', 'TEAM', 3, 3, '/1/3/7/', 1, '商户运营团队', 'ACTIVE'),
('TEAM_004', '用户运营团队', 'TEAM', 3, 3, '/1/3/8/', 2, '用户运营团队', 'ACTIVE');
