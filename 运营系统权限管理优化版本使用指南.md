# 运营系统权限管理优化版本使用指南

## 文档信息
- **版本**：1.0
- **作者**：系统设计团队
- **日期**：2025-01-27
- **状态**：草稿

## 1. 设计优化说明

基于您的设计思路，我们对原方案进行了以下优化：

### 1.1 主要优化点

1. **简化表结构**：将菜单和权限合并到一个`permission`表中，通过`type`字段区分
2. **修复SQL错误**：修正了原SQL中的语法错误
3. **增强权限表**：支持两种权限编码方式，兼容性更好
4. **保持一致性**：所有表都采用统一的字段风格

### 1.2 修复的问题

1. **字段名错误**：`organization`表中的`UNIQUE KEY uk_code (code)`应为`uk_org_code (org_code)`
2. **SQL语法错误**：`role_permission`表的CREATE语句不完整
3. **权限编码支持**：支持两种格式：`USER:READ`和`MENU_DASHBOARD`

## 2. 权限表设计说明

### 2.1 权限类型

权限表通过`type`字段支持四种类型：

- **MENU**：菜单权限，对应导航菜单
- **PAGE**：页面权限，对应具体功能页面
- **BUTTON**：按钮权限，对应页面内的操作按钮
- **API**：接口权限，对应后端API接口

### 2.2 树形结构

通过`parent_id`字段构建权限的层级关系：

```
MENU_SYSTEM (系统管理菜单)
├── PAGE_USER_MANAGEMENT (用户管理页面)
│   ├── BUTTON_USER_CREATE (新增用户按钮)
│   ├── BUTTON_USER_EDIT (编辑用户按钮)
│   ├── USER:READ (查看用户API)
│   ├── USER:CREATE (创建用户API)
│   └── USER:UPDATE (更新用户API)
└── PAGE_ROLE_MANAGEMENT (角色管理页面)
    ├── ROLE:READ (查看角色API)
    └── ROLE:CREATE (创建角色API)
```

### 2.3 权限编码规范

支持两种权限编码方式：

1. **功能编码**：`MENU_DASHBOARD`、`PAGE_USER_MANAGEMENT`、`BUTTON_USER_CREATE`
2. **模块:动作**：`USER:READ`、`ORDER:CREATE`、`MERCHANT:AUDIT`

## 3. 权限查询示例

### 3.1 查询用户菜单树

```sql
-- 查询用户可访问的菜单树
WITH RECURSIVE user_permissions AS (
    -- 获取用户的所有权限ID
    SELECT DISTINCT rp.permission_id
    FROM business_account_role bar
    JOIN role_permission rp ON bar.role_id = rp.role_id
    WHERE bar.business_account_id = ? 
      AND bar.status = 'ACTIVE' 
      AND bar.active = 1
      AND rp.grant_type = 'GRANT'
      AND rp.active = 1
),
menu_tree AS (
    -- 根菜单
    SELECT p.id, p.permission_code, p.permission_name, p.type, 
           p.parent_id, p.url, p.icon, p.order_num, 0 as level
    FROM permission p
    JOIN user_permissions up ON p.id = up.permission_id
    WHERE p.parent_id IS NULL 
      AND p.type = 'MENU'
      AND p.active = 1
    
    UNION ALL
    
    -- 子菜单
    SELECT p.id, p.permission_code, p.permission_name, p.type,
           p.parent_id, p.url, p.icon, p.order_num, mt.level + 1
    FROM permission p
    JOIN menu_tree mt ON p.parent_id = mt.id
    JOIN user_permissions up ON p.id = up.permission_id
    WHERE p.type IN ('MENU', 'PAGE')
      AND p.active = 1
)
SELECT * FROM menu_tree ORDER BY level, order_num;
```

### 3.2 查询用户页面按钮权限

```sql
-- 查询用户在特定页面的按钮权限
SELECT p.permission_code, p.permission_name
FROM business_account_role bar
JOIN role_permission rp ON bar.role_id = rp.role_id
JOIN permission p ON rp.permission_id = p.id
WHERE bar.business_account_id = ?
  AND bar.status = 'ACTIVE'
  AND bar.active = 1
  AND rp.grant_type = 'GRANT'
  AND rp.active = 1
  AND p.type = 'BUTTON'
  AND p.parent_id = (
      SELECT id FROM permission 
      WHERE permission_code = 'PAGE_USER_MANAGEMENT'
  )
  AND p.active = 1;
```

### 3.3 查询用户API权限

```sql
-- 查询用户的API权限
SELECT p.permission_code, p.url, p.http_method
FROM business_account_role bar
JOIN role_permission rp ON bar.role_id = rp.role_id
JOIN permission p ON rp.permission_id = p.id
WHERE bar.business_account_id = ?
  AND bar.status = 'ACTIVE'
  AND bar.active = 1
  AND rp.grant_type = 'GRANT'
  AND rp.active = 1
  AND p.type = 'API'
  AND p.active = 1;
```

## 4. 权限配置操作

### 4.1 创建菜单权限

```sql
-- 创建一级菜单
INSERT INTO `permission` (
    `permission_code`, `permission_name`, `type`, `parent_id`, 
    `url`, `icon`, `order_num`, `description`
) VALUES (
    'MENU_FINANCE', '财务管理', 'MENU', NULL, 
    '/finance', 'finance', 5, '财务管理菜单'
);

-- 创建二级页面
INSERT INTO `permission` (
    `permission_code`, `permission_name`, `type`, `parent_id`, 
    `url`, `component_path`, `icon`, `order_num`, `description`
) VALUES (
    'PAGE_FINANCE_REPORT', '财务报表', 'PAGE', LAST_INSERT_ID(), 
    '/finance/reports', 'Finance/Report/Index', 'finance-report', 1, '财务报表页面'
);
```

### 4.2 创建按钮权限

```sql
-- 为页面添加按钮权限
INSERT INTO `permission` (
    `permission_code`, `permission_name`, `type`, `parent_id`, 
    `order_num`, `description`
) VALUES (
    'BUTTON_FINANCE_EXPORT', '导出财务数据', 'BUTTON', 
    (SELECT id FROM permission WHERE permission_code = 'PAGE_FINANCE_REPORT'), 
    1, '财务报表导出按钮'
);
```

### 4.3 创建API权限

```sql
-- 为页面添加API权限
INSERT INTO `permission` (
    `permission_code`, `permission_name`, `type`, `parent_id`, 
    `url`, `http_method`, `order_num`, `description`
) VALUES (
    'FINANCE:READ', '查看财务数据', 'API', 
    (SELECT id FROM permission WHERE permission_code = 'PAGE_FINANCE_REPORT'), 
    '/api/finance/reports', 'GET', 1, '查看财务报表API'
);
```

## 5. 权限校验实现

### 5.1 菜单权限校验

```java
// 检查用户是否有菜单访问权限
public boolean hasMenuPermission(Long userId, String menuCode) {
    String sql = """
        SELECT COUNT(1) > 0
        FROM business_account_role bar
        JOIN role_permission rp ON bar.role_id = rp.role_id
        JOIN permission p ON rp.permission_id = p.id
        WHERE bar.business_account_id = ?
          AND bar.status = 'ACTIVE'
          AND bar.active = 1
          AND rp.grant_type = 'GRANT'
          AND rp.active = 1
          AND p.permission_code = ?
          AND p.type IN ('MENU', 'PAGE')
          AND p.active = 1
        """;
    
    return jdbcTemplate.queryForObject(sql, Boolean.class, userId, menuCode);
}
```

### 5.2 按钮权限校验

```java
// 检查用户是否有按钮操作权限
public boolean hasButtonPermission(Long userId, String buttonCode) {
    String sql = """
        SELECT COUNT(1) > 0
        FROM business_account_role bar
        JOIN role_permission rp ON bar.role_id = rp.role_id
        JOIN permission p ON rp.permission_id = p.id
        WHERE bar.business_account_id = ?
          AND bar.status = 'ACTIVE'
          AND bar.active = 1
          AND rp.grant_type = 'GRANT'
          AND rp.active = 1
          AND p.permission_code = ?
          AND p.type = 'BUTTON'
          AND p.active = 1
        """;
    
    return jdbcTemplate.queryForObject(sql, Boolean.class, userId, buttonCode);
}
```

### 5.3 API权限校验

```java
// 检查用户是否有API访问权限
public boolean hasApiPermission(Long userId, String apiPath, String httpMethod) {
    String sql = """
        SELECT COUNT(1) > 0
        FROM business_account_role bar
        JOIN role_permission rp ON bar.role_id = rp.role_id
        JOIN permission p ON rp.permission_id = p.id
        WHERE bar.business_account_id = ?
          AND bar.status = 'ACTIVE'
          AND bar.active = 1
          AND rp.grant_type = 'GRANT'
          AND rp.active = 1
          AND p.type = 'API'
          AND p.url = ?
          AND p.http_method = ?
          AND p.active = 1
        """;
    
    return jdbcTemplate.queryForObject(sql, Boolean.class, userId, apiPath, httpMethod);
}
```

## 6. 数据权限应用

### 6.1 构建数据过滤条件

```java
// 根据用户的数据权限规则构建查询条件
public String buildDataFilter(Long userId, String resourceType) {
    String sql = """
        SELECT dpr.filter_condition
        FROM business_account_role bar
        JOIN role_data_permission rdp ON bar.role_id = rdp.role_id
        JOIN data_permission_rule dpr ON rdp.data_permission_rule_id = dpr.id
        WHERE bar.business_account_id = ?
          AND bar.status = 'ACTIVE'
          AND bar.active = 1
          AND rdp.active = 1
          AND dpr.resource_type IN (?, 'ALL')
          AND dpr.active = 1
        """;
    
    List<String> conditions = jdbcTemplate.queryForList(sql, String.class, userId, resourceType);
    
    // 解析JSON条件并替换变量
    return parseAndBuildFilter(conditions, userId);
}
```

## 7. 最佳实践

### 7.1 权限设计原则

1. **层次清晰**：菜单 -> 页面 -> 按钮 -> API 的层次结构
2. **粒度适中**：既要细粒度控制，又要避免过度复杂
3. **易于维护**：权限编码要有规律，便于管理
4. **性能考虑**：合理使用缓存，避免频繁查询数据库

### 7.2 权限缓存策略

1. **用户权限缓存**：缓存用户的所有权限编码
2. **菜单树缓存**：缓存用户的菜单树结构
3. **缓存更新**：权限变更时及时清理相关缓存

### 7.3 安全建议

1. **最小权限原则**：用户只拥有必需的权限
2. **定期审查**：定期检查用户权限分配
3. **操作日志**：记录所有权限相关操作
4. **权限校验**：在每个关键操作点都要进行权限校验

## 8. 变更历史

| 版本 | 日期 | 作者 | 变更描述 | 关联功能/需求 |
|-----|------|------|---------|-------------|
| 1.0 | 2025-01-27 | 系统设计团队 | 初始版本 | 运营系统权限管理优化版本使用指南 |
