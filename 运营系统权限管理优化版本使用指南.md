# 运营系统权限管理优化版本使用指南

## 文档信息
- **版本**：2.0
- **作者**：系统设计团队
- **日期**：2025-01-27
- **状态**：正式版

## 1. 系统架构概述

基于您的设计思路，本系统采用简化而强大的权限管理架构：

### 1.1 核心设计理念

1. **统一权限表**：将菜单、页面、按钮、API权限统一管理，通过`type`字段区分
2. **树形结构**：通过`parent_id`构建权限的层级关系，支持无限层级
3. **组织架构**：支持多层级组织结构，角色可归属特定组织
4. **灵活编码**：支持两种权限编码方式，适应不同场景需求
5. **简化设计**：移除复杂的数据权限和缓存表，专注核心功能

### 1.2 表结构设计

| 表名 | 说明 | 核心功能 |
|-----|------|---------|
| `organization` | 组织表 | 支持树形组织架构，包含组织路径和层级 |
| `permission` | 权限表 | 统一管理四种权限类型，支持树形结构 |
| `role` | 角色表 | 角色定义，支持组织归属 |
| `role_permission` | 角色权限关联表 | 支持授权和拒绝两种类型 |
| `business_account_role` | 用户角色关联表 | 支持时效性和组织范围 |
| `permission_operation_log` | 权限操作日志表 | 完整的审计日志记录 |

## 2. 权限表设计详解

### 2.1 权限类型分类

权限表通过`type`字段支持四种权限类型：

| 类型 | 说明 | 使用场景 | 编码示例 |
|-----|------|---------|---------|
| **MENU** | 菜单权限 | 一级导航菜单显示控制 | `MENU_SYSTEM`、`MENU_BUSINESS` |
| **PAGE** | 页面权限 | 具体功能页面访问控制 | `PAGE_USER_MANAGEMENT`、`PAGE_ORDER_MANAGEMENT` |
| **BUTTON** | 按钮权限 | 页面内操作按钮显示控制 | `BUTTON_USER_CREATE`、`BUTTON_USER_DELETE` |
| **API** | 接口权限 | 后端API接口访问控制 | `USER:READ`、`ORDER:CREATE` |

### 2.2 树形权限结构

通过`parent_id`字段构建权限的层级关系，实现精细化权限控制：

```
MENU_SYSTEM (系统管理菜单)
├── PAGE_USER_MANAGEMENT (用户管理页面)
│   ├── BUTTON_USER_CREATE (新增用户按钮)
│   ├── BUTTON_USER_EDIT (编辑用户按钮)
│   ├── BUTTON_USER_DELETE (删除用户按钮)
│   ├── USER:READ (查看用户API)
│   ├── USER:CREATE (创建用户API)
│   ├── USER:UPDATE (更新用户API)
│   └── USER:DELETE (删除用户API)
├── PAGE_ROLE_MANAGEMENT (角色管理页面)
│   ├── BUTTON_ROLE_CREATE (新增角色按钮)
│   ├── ROLE:READ (查看角色API)
│   └── ROLE:CREATE (创建角色API)
└── PAGE_ORG_MANAGEMENT (组织管理页面)
    ├── ORGANIZATION:READ (查看组织API)
    └── ORGANIZATION:CREATE (创建组织API)
```

### 2.3 权限编码规范

系统支持两种权限编码方式，可根据实际需求选择：

#### 2.3.1 功能编码方式
- **格式**：`类型_功能名称`
- **示例**：
  - `MENU_DASHBOARD` - 仪表板菜单
  - `PAGE_USER_MANAGEMENT` - 用户管理页面
  - `BUTTON_USER_CREATE` - 新增用户按钮

#### 2.3.2 模块:动作方式
- **格式**：`模块:动作`
- **示例**：
  - `USER:READ` - 查看用户
  - `ORDER:CREATE` - 创建订单
  - `MERCHANT:AUDIT` - 审核商户

### 2.4 权限表核心字段

| 字段名 | 类型 | 说明 | 示例值 |
|-------|------|------|--------|
| `permission_code` | VARCHAR(100) | 权限唯一编码 | `USER:READ`、`MENU_SYSTEM` |
| `permission_name` | VARCHAR(100) | 权限名称 | `查看用户`、`系统管理` |
| `type` | VARCHAR(50) | 权限类型 | `MENU`、`PAGE`、`BUTTON`、`API` |
| `parent_id` | BIGINT | 父权限ID | 构建树形结构 |
| `url` | VARCHAR(255) | 页面路径或API路径 | `/system/users`、`/api/users` |
| `http_method` | VARCHAR(10) | HTTP方法 | `GET`、`POST`、`PUT`、`DELETE` |
| `component_path` | VARCHAR(200) | 组件路径 | `System/User/Index` |
| `order_num` | INT | 排序字段 | 用于菜单排序 |
| `is_visible` | TINYINT(1) | 是否可见 | 控制菜单显示隐藏 |

## 3. 组织架构设计

### 3.1 组织表结构特点

组织表支持完整的树形组织架构：

| 字段名 | 说明 | 作用 |
|-------|------|------|
| `org_code` | 组织编码 | 唯一标识，便于系统集成 |
| `org_name` | 组织名称 | 显示名称 |
| `org_type` | 组织类型 | 如：公司、部门、团队、小组 |
| `parent_id` | 父组织ID | 构建树形结构 |
| `org_level` | 组织层级 | 从1开始，便于查询和显示 |
| `org_path` | 组织路径 | 如：/1/2/3/，便于查询子组织 |
| `leader_id` | 负责人ID | 关联business_account表 |

### 3.2 组织层级示例

```
老猫点评科技有限公司 (COMPANY_001, Level 1, Path: /1/)
├── 技术部 (DEPT_TECH, Level 2, Path: /1/2/)
│   ├── 后端开发团队 (TEAM_BACKEND, Level 3, Path: /1/2/6/)
│   └── 前端开发团队 (TEAM_FRONTEND, Level 3, Path: /1/2/7/)
├── 运营部 (DEPT_OPS, Level 2, Path: /1/3/)
│   ├── 商户运营团队 (TEAM_MERCHANT_OPS, Level 3, Path: /1/3/8/)
│   └── 用户运营团队 (TEAM_USER_OPS, Level 3, Path: /1/3/9/)
└── 财务部 (DEPT_FINANCE, Level 2, Path: /1/5/)
```

## 4. 角色权限关联设计

### 4.1 角色权限关联表特点

`role_permission`表支持精细的权限控制：

- **grant_type字段**：支持`GRANT`（授权）和`DENY`（拒绝）两种类型
- **优先级规则**：`DENY`优先级高于`GRANT`，实现权限的精确控制
- **批量管理**：支持批量授权和撤销

### 4.2 用户角色关联表特点

`business_account_role`表支持高级功能：

- **组织范围**：通过`org_id`限定角色在特定组织内有效
- **时效控制**：通过`effective_date`和`expiry_date`控制角色生效时间
- **状态管理**：通过`status`字段管理角色状态（ACTIVE、INACTIVE、EXPIRED）

## 5. 核心查询示例

### 5.1 查询用户菜单树

```sql
-- 查询用户可访问的完整菜单树
WITH RECURSIVE user_permissions AS (
    -- 获取用户的所有有效权限
    SELECT DISTINCT rp.permission_id
    FROM business_account_role bar
    JOIN role_permission rp ON bar.role_id = rp.role_id
    WHERE bar.business_account_id = ?
      AND bar.status = 'ACTIVE'
      AND bar.active = 1
      AND (bar.effective_date IS NULL OR bar.effective_date <= CURDATE())
      AND (bar.expiry_date IS NULL OR bar.expiry_date >= CURDATE())
      AND rp.grant_type = 'GRANT'
      AND rp.active = 1

    -- 排除被拒绝的权限
    EXCEPT

    SELECT DISTINCT rp.permission_id
    FROM business_account_role bar
    JOIN role_permission rp ON bar.role_id = rp.role_id
    WHERE bar.business_account_id = ?
      AND bar.status = 'ACTIVE'
      AND bar.active = 1
      AND (bar.effective_date IS NULL OR bar.effective_date <= CURDATE())
      AND (bar.expiry_date IS NULL OR bar.expiry_date >= CURDATE())
      AND rp.grant_type = 'DENY'
      AND rp.active = 1
),
menu_tree AS (
    -- 根菜单
    SELECT p.id, p.permission_code, p.permission_name, p.type,
           p.parent_id, p.url, p.icon, p.component_path, p.order_num,
           p.is_visible, 0 as level
    FROM permission p
    JOIN user_permissions up ON p.id = up.permission_id
    WHERE p.parent_id IS NULL
      AND p.type = 'MENU'
      AND p.active = 1
      AND p.is_visible = 1

    UNION ALL

    -- 子菜单和页面
    SELECT p.id, p.permission_code, p.permission_name, p.type,
           p.parent_id, p.url, p.icon, p.component_path, p.order_num,
           p.is_visible, mt.level + 1
    FROM permission p
    JOIN menu_tree mt ON p.parent_id = mt.id
    JOIN user_permissions up ON p.id = up.permission_id
    WHERE p.type IN ('MENU', 'PAGE')
      AND p.active = 1
      AND p.is_visible = 1
)
SELECT * FROM menu_tree ORDER BY level, order_num;
```

### 5.2 查询用户按钮权限

```sql
-- 查询用户在特定页面的按钮权限
SELECT p.permission_code, p.permission_name, p.order_num
FROM business_account_role bar
JOIN role_permission rp ON bar.role_id = rp.role_id
JOIN permission p ON rp.permission_id = p.id
WHERE bar.business_account_id = ?
  AND bar.status = 'ACTIVE'
  AND bar.active = 1
  AND (bar.effective_date IS NULL OR bar.effective_date <= CURDATE())
  AND (bar.expiry_date IS NULL OR bar.expiry_date >= CURDATE())
  AND rp.grant_type = 'GRANT'
  AND rp.active = 1
  AND p.type = 'BUTTON'
  AND p.parent_id = (
      SELECT id FROM permission
      WHERE permission_code = ? -- 传入页面权限编码
  )
  AND p.active = 1
  AND p.permission_id NOT IN (
      -- 排除被拒绝的按钮权限
      SELECT rp2.permission_id
      FROM business_account_role bar2
      JOIN role_permission rp2 ON bar2.role_id = rp2.role_id
      WHERE bar2.business_account_id = ?
        AND bar2.status = 'ACTIVE'
        AND bar2.active = 1
        AND rp2.grant_type = 'DENY'
        AND rp2.active = 1
  )
ORDER BY p.order_num;
```

### 5.3 查询用户API权限

```sql
-- 检查用户是否有特定API权限
SELECT COUNT(1) > 0 as has_permission
FROM business_account_role bar
JOIN role_permission rp ON bar.role_id = rp.role_id
JOIN permission p ON rp.permission_id = p.id
WHERE bar.business_account_id = ?
  AND bar.status = 'ACTIVE'
  AND bar.active = 1
  AND (bar.effective_date IS NULL OR bar.effective_date <= CURDATE())
  AND (bar.expiry_date IS NULL OR bar.expiry_date >= CURDATE())
  AND rp.grant_type = 'GRANT'
  AND rp.active = 1
  AND p.permission_code = ?
  AND p.type = 'API'
  AND p.active = 1
  AND p.id NOT IN (
      -- 排除被拒绝的API权限
      SELECT rp2.permission_id
      FROM business_account_role bar2
      JOIN role_permission rp2 ON bar2.role_id = rp2.role_id
      WHERE bar2.business_account_id = ?
        AND bar2.status = 'ACTIVE'
        AND bar2.active = 1
        AND rp2.grant_type = 'DENY'
        AND rp2.active = 1
  );
```

### 5.4 查询组织下的所有用户

```sql
-- 查询指定组织及其子组织下的所有用户
SELECT ba.id, ba.account_name, o.org_name, o.org_level
FROM business_account ba
JOIN organization o ON ba.org_id = o.id
WHERE o.org_path LIKE CONCAT(
    (SELECT org_path FROM organization WHERE id = ?), '%'
)
AND ba.active = 1
ORDER BY o.org_level, o.sort_order;
```

## 6. 权限配置操作

### 6.1 创建完整的权限体系

以财务管理模块为例，展示如何创建完整的权限体系：

```sql
-- 1. 创建一级菜单
INSERT INTO `permission` (
    `permission_code`, `permission_name`, `type`, `parent_id`,
    `url`, `icon`, `order_num`, `is_visible`, `description`
) VALUES (
    'MENU_FINANCE', '财务管理', 'MENU', NULL,
    '/finance', 'finance', 5, 1, '财务管理菜单'
);

-- 2. 创建二级页面
INSERT INTO `permission` (
    `permission_code`, `permission_name`, `type`, `parent_id`,
    `url`, `component_path`, `order_num`, `is_visible`, `description`
) VALUES (
    'PAGE_FINANCE_REPORT', '财务报表', 'PAGE',
    (SELECT id FROM permission WHERE permission_code = 'MENU_FINANCE'),
    '/finance/reports', 'Finance/Report/Index', 1, 1, '财务报表页面'
),
(
    'PAGE_SETTLEMENT_MANAGEMENT', '结算管理', 'PAGE',
    (SELECT id FROM permission WHERE permission_code = 'MENU_FINANCE'),
    '/finance/settlements', 'Finance/Settlement/Index', 2, 1, '结算管理页面'
);

-- 3. 创建按钮权限
INSERT INTO `permission` (
    `permission_code`, `permission_name`, `type`, `parent_id`,
    `order_num`, `is_visible`, `description`
) VALUES (
    'BUTTON_FINANCE_EXPORT', '导出财务数据', 'BUTTON',
    (SELECT id FROM permission WHERE permission_code = 'PAGE_FINANCE_REPORT'),
    1, 1, '财务报表导出按钮'
),
(
    'BUTTON_SETTLEMENT_CREATE', '创建结算', 'BUTTON',
    (SELECT id FROM permission WHERE permission_code = 'PAGE_SETTLEMENT_MANAGEMENT'),
    1, 1, '创建结算按钮'
);

-- 4. 创建API权限
INSERT INTO `permission` (
    `permission_code`, `permission_name`, `type`, `parent_id`,
    `url`, `http_method`, `order_num`, `description`
) VALUES (
    'FINANCE:READ', '查看财务数据', 'API',
    (SELECT id FROM permission WHERE permission_code = 'PAGE_FINANCE_REPORT'),
    '/api/finance/reports', 'GET', 1, '查看财务报表API'
),
(
    'FINANCE:EXPORT', '导出财务数据', 'API',
    (SELECT id FROM permission WHERE permission_code = 'PAGE_FINANCE_REPORT'),
    '/api/finance/reports/export', 'GET', 2, '导出财务报表API'
),
(
    'SETTLEMENT:READ', '查看结算数据', 'API',
    (SELECT id FROM permission WHERE permission_code = 'PAGE_SETTLEMENT_MANAGEMENT'),
    '/api/finance/settlements', 'GET', 1, '查看结算数据API'
),
(
    'SETTLEMENT:CREATE', '创建结算', 'API',
    (SELECT id FROM permission WHERE permission_code = 'PAGE_SETTLEMENT_MANAGEMENT'),
    '/api/finance/settlements', 'POST', 2, '创建结算记录API'
);
```

### 6.2 角色权限管理

```sql
-- 1. 创建角色
INSERT INTO `role` (`role_code`, `role_name`, `org_id`, `description`)
VALUES ('FINANCE_MANAGER', '财务经理', 5, '财务部门经理角色');

-- 2. 批量为角色分配权限
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`)
SELECT
    (SELECT id FROM role WHERE role_code = 'FINANCE_MANAGER'),
    p.id,
    'GRANT'
FROM permission p
WHERE p.permission_code IN (
    'MENU_FINANCE',
    'PAGE_FINANCE_REPORT',
    'PAGE_SETTLEMENT_MANAGEMENT',
    'BUTTON_FINANCE_EXPORT',
    'BUTTON_SETTLEMENT_CREATE',
    'FINANCE:READ',
    'FINANCE:EXPORT',
    'SETTLEMENT:READ',
    'SETTLEMENT:CREATE'
);

-- 3. 为用户分配角色
INSERT INTO `business_account_role` (
    `business_account_id`, `role_id`, `org_id`,
    `effective_date`, `expiry_date`, `status`
) VALUES (
    1001,
    (SELECT id FROM role WHERE role_code = 'FINANCE_MANAGER'),
    5, '2025-01-27', '2025-12-31', 'ACTIVE'
);

-- 4. 权限拒绝示例（特殊情况下拒绝某个权限）
INSERT INTO `role_permission` (`role_id`, `permission_id`, `grant_type`)
VALUES (
    (SELECT id FROM role WHERE role_code = 'FINANCE_MANAGER'),
    (SELECT id FROM permission WHERE permission_code = 'SETTLEMENT:CREATE'),
    'DENY'  -- 拒绝创建结算权限
);
```

### 6.3 组织管理操作

```sql
-- 1. 创建组织
INSERT INTO `organization` (
    `org_name`, `org_code`, `org_type`, `parent_id`, `org_level`,
    `org_path`, `sort_order`, `leader_id`, `description`
) VALUES (
    '财务部', 'DEPT_FINANCE', '部门', 1, 2,
    '/1/5/', 4, 1001, '财务管理部门'
);

-- 2. 更新组织路径（当组织结构变更时）
UPDATE organization
SET org_path = CONCAT(
    (SELECT org_path FROM organization WHERE id = parent_id),
    id, '/'
),
org_level = (
    SELECT org_level + 1 FROM organization WHERE id = parent_id
)
WHERE parent_id IS NOT NULL;

-- 3. 查询组织树
WITH RECURSIVE org_tree AS (
    -- 根组织
    SELECT id, org_name, org_code, org_type, parent_id,
           org_level, org_path, 0 as depth
    FROM organization
    WHERE parent_id IS NULL AND active = 1

    UNION ALL

    -- 子组织
    SELECT o.id, o.org_name, o.org_code, o.org_type, o.parent_id,
           o.org_level, o.org_path, ot.depth + 1
    FROM organization o
    JOIN org_tree ot ON o.parent_id = ot.id
    WHERE o.active = 1
)
SELECT * FROM org_tree ORDER BY org_level, sort_order;
```

## 7. 权限校验实现

### 7.1 Java服务层实现

```java
@Service
@Slf4j
public class PermissionService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String PERMISSION_CACHE_PREFIX = "user:permissions:";
    private static final int CACHE_EXPIRE_HOURS = 2;

    /**
     * 检查用户是否有特定权限（支持GRANT/DENY逻辑）
     */
    public boolean hasPermission(Long userId, String permissionCode) {
        // 1. 先检查缓存
        String cacheKey = PERMISSION_CACHE_PREFIX + userId;
        Set<String> cachedPermissions = (Set<String>) redisTemplate.opsForValue().get(cacheKey);

        if (cachedPermissions != null) {
            return cachedPermissions.contains(permissionCode);
        }

        // 2. 从数据库查询
        Set<String> userPermissions = getUserPermissionsFromDB(userId);

        // 3. 缓存结果
        redisTemplate.opsForValue().set(cacheKey, userPermissions,
            Duration.ofHours(CACHE_EXPIRE_HOURS));

        return userPermissions.contains(permissionCode);
    }

    /**
     * 从数据库获取用户权限（考虑GRANT/DENY逻辑）
     */
    private Set<String> getUserPermissionsFromDB(Long userId) {
        String sql = """
            SELECT DISTINCT p.permission_code
            FROM business_account_role bar
            JOIN role_permission rp ON bar.role_id = rp.role_id
            JOIN permission p ON rp.permission_id = p.id
            WHERE bar.business_account_id = ?
              AND bar.status = 'ACTIVE'
              AND bar.active = 1
              AND (bar.effective_date IS NULL OR bar.effective_date <= CURDATE())
              AND (bar.expiry_date IS NULL OR bar.expiry_date >= CURDATE())
              AND rp.grant_type = 'GRANT'
              AND rp.active = 1
              AND p.active = 1
              AND p.id NOT IN (
                  -- 排除被拒绝的权限
                  SELECT rp2.permission_id
                  FROM business_account_role bar2
                  JOIN role_permission rp2 ON bar2.role_id = rp2.role_id
                  WHERE bar2.business_account_id = ?
                    AND bar2.status = 'ACTIVE'
                    AND bar2.active = 1
                    AND (bar2.effective_date IS NULL OR bar2.effective_date <= CURDATE())
                    AND (bar2.expiry_date IS NULL OR bar2.expiry_date >= CURDATE())
                    AND rp2.grant_type = 'DENY'
                    AND rp2.active = 1
              )
            """;

        List<String> permissions = jdbcTemplate.queryForList(sql, String.class, userId, userId);
        return new HashSet<>(permissions);
    }

    /**
     * 获取用户菜单树
     */
    public List<MenuTreeNode> getUserMenuTree(Long userId) {
        String sql = """
            WITH RECURSIVE user_permissions AS (
                SELECT DISTINCT rp.permission_id
                FROM business_account_role bar
                JOIN role_permission rp ON bar.role_id = rp.role_id
                WHERE bar.business_account_id = ?
                  AND bar.status = 'ACTIVE'
                  AND bar.active = 1
                  AND (bar.effective_date IS NULL OR bar.effective_date <= CURDATE())
                  AND (bar.expiry_date IS NULL OR bar.expiry_date >= CURDATE())
                  AND rp.grant_type = 'GRANT'
                  AND rp.active = 1
                EXCEPT
                SELECT DISTINCT rp.permission_id
                FROM business_account_role bar
                JOIN role_permission rp ON bar.role_id = rp.role_id
                WHERE bar.business_account_id = ?
                  AND bar.status = 'ACTIVE'
                  AND bar.active = 1
                  AND (bar.effective_date IS NULL OR bar.effective_date <= CURDATE())
                  AND (bar.expiry_date IS NULL OR bar.expiry_date >= CURDATE())
                  AND rp.grant_type = 'DENY'
                  AND rp.active = 1
            ),
            menu_tree AS (
                SELECT p.id, p.permission_code, p.permission_name, p.type,
                       p.parent_id, p.url, p.icon, p.component_path, p.order_num,
                       p.is_visible, 0 as level
                FROM permission p
                JOIN user_permissions up ON p.id = up.permission_id
                WHERE p.parent_id IS NULL
                  AND p.type = 'MENU'
                  AND p.active = 1
                  AND p.is_visible = 1

                UNION ALL

                SELECT p.id, p.permission_code, p.permission_name, p.type,
                       p.parent_id, p.url, p.icon, p.component_path, p.order_num,
                       p.is_visible, mt.level + 1
                FROM permission p
                JOIN menu_tree mt ON p.parent_id = mt.id
                JOIN user_permissions up ON p.id = up.permission_id
                WHERE p.type IN ('MENU', 'PAGE')
                  AND p.active = 1
                  AND p.is_visible = 1
            )
            SELECT * FROM menu_tree ORDER BY level, order_num
            """;

        return jdbcTemplate.query(sql, new MenuTreeNodeRowMapper(), userId, userId);
    }

    /**
     * 获取用户在特定页面的按钮权限
     */
    public List<String> getUserButtonPermissions(Long userId, String pageCode) {
        String sql = """
            SELECT p.permission_code
            FROM business_account_role bar
            JOIN role_permission rp ON bar.role_id = rp.role_id
            JOIN permission p ON rp.permission_id = p.id
            WHERE bar.business_account_id = ?
              AND bar.status = 'ACTIVE'
              AND bar.active = 1
              AND (bar.effective_date IS NULL OR bar.effective_date <= CURDATE())
              AND (bar.expiry_date IS NULL OR bar.expiry_date >= CURDATE())
              AND rp.grant_type = 'GRANT'
              AND rp.active = 1
              AND p.type = 'BUTTON'
              AND p.parent_id = (SELECT id FROM permission WHERE permission_code = ?)
              AND p.active = 1
              AND p.id NOT IN (
                  SELECT rp2.permission_id
                  FROM business_account_role bar2
                  JOIN role_permission rp2 ON bar2.role_id = rp2.role_id
                  WHERE bar2.business_account_id = ?
                    AND bar2.status = 'ACTIVE'
                    AND bar2.active = 1
                    AND rp2.grant_type = 'DENY'
                    AND rp2.active = 1
              )
            ORDER BY p.order_num
            """;

        return jdbcTemplate.queryForList(sql, String.class, userId, pageCode, userId);
    }

    /**
     * 清除用户权限缓存
     */
    public void clearUserPermissionCache(Long userId) {
        String cacheKey = PERMISSION_CACHE_PREFIX + userId;
        redisTemplate.delete(cacheKey);
        log.info("Cleared permission cache for user: {}", userId);
    }

    /**
     * 批量清除权限缓存
     */
    public void clearAllPermissionCache() {
        Set<String> keys = redisTemplate.keys(PERMISSION_CACHE_PREFIX + "*");
        if (keys != null && !keys.isEmpty()) {
            redisTemplate.delete(keys);
            log.info("Cleared all permission cache, count: {}", keys.size());
        }
    }
}
```

### 7.2 权限注解和AOP实现

```java
// 权限校验注解
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequirePermission {
    /**
     * 权限编码
     */
    String value();

    /**
     * 权限描述
     */
    String description() default "";

    /**
     * 是否记录访问日志
     */
    boolean logAccess() default true;
}

// AOP权限校验切面
@Aspect
@Component
@Slf4j
public class PermissionAspect {

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private PermissionLogService permissionLogService;

    @Around("@annotation(requirePermission)")
    public Object checkPermission(ProceedingJoinPoint joinPoint, RequirePermission requirePermission) throws Throwable {
        // 1. 获取当前用户ID
        Long userId = getCurrentUserId();
        if (userId == null) {
            throw new UnauthorizedException("用户未登录");
        }

        // 2. 检查权限
        String permissionCode = requirePermission.value();
        boolean hasPermission = permissionService.hasPermission(userId, permissionCode);

        if (!hasPermission) {
            // 记录权限拒绝日志
            if (requirePermission.logAccess()) {
                permissionLogService.logPermissionDenied(userId, permissionCode,
                    joinPoint.getSignature().toShortString());
            }
            throw new ForbiddenException("权限不足：" + requirePermission.description());
        }

        // 3. 执行目标方法
        Object result = joinPoint.proceed();

        // 4. 记录访问日志
        if (requirePermission.logAccess()) {
            permissionLogService.logPermissionAccess(userId, permissionCode,
                joinPoint.getSignature().toShortString());
        }

        return result;
    }

    private Long getCurrentUserId() {
        // 从SecurityContext或Session中获取当前用户ID
        // 具体实现根据项目的认证方式而定
        return UserContextHolder.getCurrentUserId();
    }
}
```

### 7.3 Controller层使用示例

```java
@RestController
@RequestMapping("/api/users")
@Slf4j
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private PermissionService permissionService;

    @GetMapping
    @RequirePermission(value = "USER:READ", description = "查看用户列表")
    public ResponseEntity<PageResult<UserDTO>> getUsers(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "20") int size) {

        PageResult<UserDTO> result = userService.getUsers(page, size);
        return ResponseEntity.ok(result);
    }

    @PostMapping
    @RequirePermission(value = "USER:CREATE", description = "创建用户")
    public ResponseEntity<UserDTO> createUser(@RequestBody @Valid CreateUserRequest request) {
        UserDTO user = userService.createUser(request);
        return ResponseEntity.ok(user);
    }

    @PutMapping("/{id}")
    @RequirePermission(value = "USER:UPDATE", description = "更新用户")
    public ResponseEntity<UserDTO> updateUser(
            @PathVariable Long id,
            @RequestBody @Valid UpdateUserRequest request) {

        UserDTO user = userService.updateUser(id, request);
        return ResponseEntity.ok(user);
    }

    @DeleteMapping("/{id}")
    @RequirePermission(value = "USER:DELETE", description = "删除用户")
    public ResponseEntity<Void> deleteUser(@PathVariable Long id) {
        userService.deleteUser(id);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/export")
    @RequirePermission(value = "USER:EXPORT", description = "导出用户数据")
    public ResponseEntity<byte[]> exportUsers() {
        byte[] data = userService.exportUsers();
        return ResponseEntity.ok()
                .header("Content-Disposition", "attachment; filename=users.xlsx")
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(data);
    }

    // 获取当前用户的按钮权限
    @GetMapping("/permissions/buttons")
    public ResponseEntity<List<String>> getUserButtonPermissions(
            @RequestParam String pageCode) {

        Long userId = UserContextHolder.getCurrentUserId();
        List<String> permissions = permissionService.getUserButtonPermissions(userId, pageCode);
        return ResponseEntity.ok(permissions);
    }
}
```

## 8. 前端集成示例

### 8.1 Vue.js权限指令

```javascript
// 权限指令
import { createApp } from 'vue'

const app = createApp({})

// 注册权限指令
app.directive('permission', {
  mounted(el, binding) {
    const { value } = binding
    const permissions = store.getters.permissions || []

    if (value && !permissions.includes(value)) {
      el.parentNode && el.parentNode.removeChild(el)
    }
  },
  updated(el, binding) {
    const { value } = binding
    const permissions = store.getters.permissions || []

    if (value && !permissions.includes(value)) {
      el.parentNode && el.parentNode.removeChild(el)
    }
  }
})

// 使用示例
// <button v-permission="'USER:CREATE'">新增用户</button>
// <button v-permission="'USER:DELETE'">删除用户</button>
```

### 8.2 React权限组件

```jsx
// 权限组件
import React from 'react'
import { useSelector } from 'react-redux'

const PermissionWrapper = ({ permission, children, fallback = null }) => {
  const permissions = useSelector(state => state.auth.permissions || [])

  if (!permission || permissions.includes(permission)) {
    return children
  }

  return fallback
}

// 使用示例
const UserManagement = () => {
  return (
    <div>
      <PermissionWrapper permission="USER:CREATE">
        <button>新增用户</button>
      </PermissionWrapper>

      <PermissionWrapper permission="USER:DELETE">
        <button>删除用户</button>
      </PermissionWrapper>
    </div>
  )
}
```

### 8.3 菜单权限过滤

```javascript
// 菜单权限过滤函数
function filterMenuByPermissions(menus, permissions) {
  return menus.filter(menu => {
    // 检查当前菜单权限
    if (menu.permission && !permissions.includes(menu.permission)) {
      return false
    }

    // 递归过滤子菜单
    if (menu.children && menu.children.length > 0) {
      menu.children = filterMenuByPermissions(menu.children, permissions)
      // 如果子菜单全部被过滤掉，则隐藏父菜单
      return menu.children.length > 0
    }

    return true
  })
}

// 使用示例
const userPermissions = ['MENU_SYSTEM', 'PAGE_USER_MANAGEMENT', 'USER:READ']
const filteredMenus = filterMenuByPermissions(allMenus, userPermissions)
```

## 9. 性能优化建议

### 9.1 数据库优化

```sql
-- 1. 为常用查询创建复合索引
CREATE INDEX idx_bar_user_status_active ON business_account_role
(business_account_id, status, active, effective_date, expiry_date);

CREATE INDEX idx_rp_role_grant_active ON role_permission
(role_id, grant_type, active);

CREATE INDEX idx_permission_type_active ON permission
(type, active, is_visible);

-- 2. 权限查询视图（可选）
CREATE VIEW v_user_permissions AS
SELECT
    bar.business_account_id,
    p.permission_code,
    p.permission_name,
    p.type,
    rp.grant_type
FROM business_account_role bar
JOIN role_permission rp ON bar.role_id = rp.role_id
JOIN permission p ON rp.permission_id = p.id
WHERE bar.status = 'ACTIVE'
  AND bar.active = 1
  AND (bar.effective_date IS NULL OR bar.effective_date <= CURDATE())
  AND (bar.expiry_date IS NULL OR bar.expiry_date >= CURDATE())
  AND rp.active = 1
  AND p.active = 1;
```

### 9.2 缓存策略

```java
@Service
public class PermissionCacheService {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String USER_PERMISSIONS_KEY = "user:permissions:";
    private static final String USER_MENU_KEY = "user:menu:";
    private static final String PERMISSION_VERSION_KEY = "permission:version";

    /**
     * 获取权限版本号（用于缓存失效）
     */
    public String getPermissionVersion() {
        String version = (String) redisTemplate.opsForValue().get(PERMISSION_VERSION_KEY);
        if (version == null) {
            version = String.valueOf(System.currentTimeMillis());
            redisTemplate.opsForValue().set(PERMISSION_VERSION_KEY, version);
        }
        return version;
    }

    /**
     * 更新权限版本号（权限变更时调用）
     */
    public void updatePermissionVersion() {
        String newVersion = String.valueOf(System.currentTimeMillis());
        redisTemplate.opsForValue().set(PERMISSION_VERSION_KEY, newVersion);

        // 清除所有用户权限缓存
        clearAllUserPermissionCache();
    }

    /**
     * 批量预热用户权限缓存
     */
    @Async
    public void preloadUserPermissions(List<Long> userIds) {
        for (Long userId : userIds) {
            try {
                permissionService.hasPermission(userId, "DUMMY"); // 触发缓存加载
            } catch (Exception e) {
                log.warn("Failed to preload permissions for user: {}", userId, e);
            }
        }
    }
}
```

## 10. 最佳实践总结

### 10.1 权限设计原则

1. **最小权限原则**：用户只拥有完成工作所需的最小权限
2. **职责分离**：不同职责的用户拥有不同的权限集合
3. **层次清晰**：菜单 -> 页面 -> 按钮 -> API 的清晰层次
4. **易于维护**：权限编码规范化，便于管理和理解
5. **审计友好**：完整的操作日志，便于安全审计

### 10.2 性能优化策略

1. **合理缓存**：
   - 用户权限缓存2小时
   - 菜单树缓存4小时
   - 权限变更时及时清理缓存

2. **数据库优化**：
   - 为常用查询字段创建索引
   - 使用视图简化复杂查询
   - 定期清理过期数据

3. **查询优化**：
   - 批量查询减少数据库访问
   - 使用递归CTE查询树形结构
   - 避免N+1查询问题

### 10.3 安全建议

1. **权限校验**：
   - 每个API接口都要进行权限校验
   - 前端权限控制仅用于用户体验，不能作为安全依赖
   - 使用AOP统一处理权限校验逻辑

2. **日志审计**：
   - 记录所有权限相关操作
   - 记录权限校验失败的尝试
   - 定期分析异常访问模式

3. **定期维护**：
   - 定期审查用户权限分配
   - 及时回收离职人员权限
   - 清理过期的角色分配

### 10.4 常见问题解决

1. **权限不生效**：
   - 检查角色是否在有效期内
   - 检查是否有DENY权限覆盖
   - 清理权限缓存重新加载

2. **性能问题**：
   - 检查数据库索引是否合理
   - 优化权限查询SQL
   - 调整缓存策略

3. **菜单不显示**：
   - 检查菜单权限配置
   - 检查is_visible字段设置
   - 验证前端权限过滤逻辑

## 11. 变更历史

| 版本 | 日期 | 作者 | 变更描述 | 关联功能/需求 |
|-----|------|------|---------|-------------|
| 2.0 | 2025-01-27 | 系统设计团队 | 基于用户修改后的SQL重新生成完整指南 | 运营系统权限管理优化版本 |
| 1.0 | 2025-01-27 | 系统设计团队 | 初始版本 | 运营系统权限管理优化版本使用指南 |
