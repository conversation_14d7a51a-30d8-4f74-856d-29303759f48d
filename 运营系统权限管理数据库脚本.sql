-- 运营系统权限管理数据库脚本
-- 版本：1.0
-- 作者：系统设计团队
-- 日期：2025-01-27

-- =============================================
-- 1. 组织架构相关表
-- =============================================

-- 组织表
CREATE TABLE `organization` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `org_code` varchar(50) NOT NULL COMMENT '组织编码，唯一',
  `org_name` varchar(100) NOT NULL COMMENT '组织名称',
  `org_type` varchar(20) NOT NULL COMMENT '组织类型：COMPANY-公司，DEPARTMENT-部门，TEAM-团队，GROUP-小组',
  `parent_id` bigint DEFAULT NULL COMMENT '父组织ID',
  `org_level` int NOT NULL DEFAULT '1' COMMENT '组织层级，从1开始',
  `org_path` varchar(500) DEFAULT NULL COMMENT '组织路径，如：/1/2/3/',
  `sort_order` int DEFAULT '0' COMMENT '排序顺序',
  `leader_id` bigint DEFAULT NULL COMMENT '负责人ID，关联business_account表',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `address` varchar(255) DEFAULT NULL COMMENT '办公地址',
  `description` varchar(500) DEFAULT NULL COMMENT '组织描述',
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-启用，INACTIVE-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_org_code` (`org_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_org_type` (`org_type`),
  KEY `idx_org_level` (`org_level`),
  KEY `idx_leader_id` (`leader_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='组织表';

-- 用户组织关联表
CREATE TABLE `user_organization` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `business_account_id` bigint NOT NULL COMMENT '商户账户ID',
  `organization_id` bigint NOT NULL COMMENT '组织ID',
  `position` varchar(50) DEFAULT NULL COMMENT '职位',
  `is_primary` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否主要组织：0-否，1-是',
  `join_date` date DEFAULT NULL COMMENT '加入日期',
  `leave_date` date DEFAULT NULL COMMENT '离职日期',
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-在职，INACTIVE-离职',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_org_primary` (`business_account_id`, `is_primary`),
  KEY `idx_business_account_id` (`business_account_id`),
  KEY `idx_organization_id` (`organization_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户组织关联表';

-- =============================================
-- 2. 权限体系相关表
-- =============================================

-- 权限分组表
CREATE TABLE `permission_group` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `group_code` varchar(50) NOT NULL COMMENT '权限分组编码，唯一',
  `group_name` varchar(100) NOT NULL COMMENT '权限分组名称',
  `parent_id` bigint DEFAULT NULL COMMENT '父分组ID',
  `group_level` int NOT NULL DEFAULT '1' COMMENT '分组层级，从1开始',
  `sort_order` int DEFAULT '0' COMMENT '排序顺序',
  `icon` varchar(100) DEFAULT NULL COMMENT '图标',
  `description` varchar(500) DEFAULT NULL COMMENT '分组描述',
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-启用，INACTIVE-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_group_code` (`group_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_group_level` (`group_level`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限分组表';

-- 权限表
CREATE TABLE `permission` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `permission_code` varchar(100) NOT NULL COMMENT '权限编码，格式：模块:动作，如USER:READ',
  `permission_name` varchar(100) NOT NULL COMMENT '权限名称',
  `permission_group_id` bigint DEFAULT NULL COMMENT '权限分组ID',
  `resource_type` varchar(20) NOT NULL COMMENT '资源类型：MENU-菜单，BUTTON-按钮，API-接口，DATA-数据',
  `resource_path` varchar(200) DEFAULT NULL COMMENT '资源路径，如菜单路径、API路径',
  `http_method` varchar(10) DEFAULT NULL COMMENT 'HTTP方法：GET、POST、PUT、DELETE等',
  `description` varchar(500) DEFAULT NULL COMMENT '权限描述',
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-启用，INACTIVE-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_permission_code` (`permission_code`),
  KEY `idx_permission_group_id` (`permission_group_id`),
  KEY `idx_resource_type` (`resource_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- =============================================
-- 3. 菜单体系相关表
-- =============================================

-- 菜单表
CREATE TABLE `menu` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `menu_code` varchar(50) NOT NULL COMMENT '菜单编码，唯一',
  `menu_name` varchar(100) NOT NULL COMMENT '菜单名称',
  `menu_type` varchar(20) NOT NULL COMMENT '菜单类型：DIRECTORY-目录，MENU-菜单，BUTTON-按钮',
  `parent_id` bigint DEFAULT NULL COMMENT '父菜单ID',
  `menu_level` int NOT NULL DEFAULT '1' COMMENT '菜单层级，从1开始',
  `menu_path` varchar(500) DEFAULT NULL COMMENT '菜单路径，如：/1/2/3/',
  `route_path` varchar(200) DEFAULT NULL COMMENT '路由路径',
  `component_path` varchar(200) DEFAULT NULL COMMENT '组件路径',
  `icon` varchar(100) DEFAULT NULL COMMENT '图标',
  `sort_order` int DEFAULT '0' COMMENT '排序顺序',
  `is_visible` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否可见：0-隐藏，1-显示',
  `is_cache` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否缓存：0-不缓存，1-缓存',
  `is_external` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否外链：0-否，1-是',
  `external_url` varchar(500) DEFAULT NULL COMMENT '外链地址',
  `description` varchar(500) DEFAULT NULL COMMENT '菜单描述',
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-启用，INACTIVE-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_menu_code` (`menu_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_menu_type` (`menu_type`),
  KEY `idx_menu_level` (`menu_level`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜单表';

-- 菜单权限关联表
CREATE TABLE `menu_permission` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  `permission_id` bigint NOT NULL COMMENT '权限ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_menu_permission` (`menu_id`, `permission_id`),
  KEY `idx_menu_id` (`menu_id`),
  KEY `idx_permission_id` (`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜单权限关联表';

-- =============================================
-- 4. 角色权限相关表
-- =============================================

-- 角色权限关联表
CREATE TABLE `role_permission` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_id` bigint NOT NULL COMMENT '角色ID，关联role表',
  `permission_id` bigint NOT NULL COMMENT '权限ID',
  `grant_type` varchar(20) NOT NULL DEFAULT 'GRANT' COMMENT '授权类型：GRANT-授权，DENY-拒绝',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_permission` (`role_id`, `permission_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_permission_id` (`permission_id`),
  KEY `idx_grant_type` (`grant_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- 为现有的business_account_role表添加字段
ALTER TABLE `business_account_role`
ADD COLUMN `organization_id` bigint DEFAULT NULL COMMENT '组织ID，角色在特定组织内有效' AFTER `role_id`,
ADD COLUMN `effective_date` date DEFAULT NULL COMMENT '生效日期' AFTER `organization_id`,
ADD COLUMN `expiry_date` date DEFAULT NULL COMMENT '失效日期' AFTER `effective_date`,
ADD COLUMN `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-生效，INACTIVE-失效，EXPIRED-过期' AFTER `expiry_date`;

-- 添加索引
ALTER TABLE `business_account_role`
ADD KEY `idx_organization_id` (`organization_id`),
ADD KEY `idx_status` (`status`),
ADD KEY `idx_effective_date` (`effective_date`),
ADD KEY `idx_expiry_date` (`expiry_date`);

-- =============================================
-- 5. 数据权限相关表
-- =============================================

-- 数据权限规则表
CREATE TABLE `data_permission_rule` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_code` varchar(50) NOT NULL COMMENT '规则编码，唯一',
  `rule_name` varchar(100) NOT NULL COMMENT '规则名称',
  `rule_type` varchar(20) NOT NULL COMMENT '规则类型：SELF-本人，DEPT-本部门，DEPT_SUB-本部门及下级，ORG-本组织，ORG_SUB-本组织及下级，ALL-全部',
  `resource_type` varchar(50) NOT NULL COMMENT '资源类型，如：USER、ORDER、MERCHANT等',
  `filter_condition` text DEFAULT NULL COMMENT '过滤条件，JSON格式',
  `description` varchar(500) DEFAULT NULL COMMENT '规则描述',
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '状态：ACTIVE-启用，INACTIVE-禁用',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_rule_code` (`rule_code`),
  KEY `idx_rule_type` (`rule_type`),
  KEY `idx_resource_type` (`resource_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据权限规则表';

-- 角色数据权限关联表
CREATE TABLE `role_data_permission` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_id` bigint NOT NULL COMMENT '角色ID，关联role表',
  `data_permission_rule_id` bigint NOT NULL COMMENT '数据权限规则ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用 0-禁用(删除) 1-启用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_data_permission` (`role_id`, `data_permission_rule_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_data_permission_rule_id` (`data_permission_rule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色数据权限关联表';

-- =============================================
-- 6. 权限缓存表（可选）
-- =============================================

-- 用户权限缓存表
CREATE TABLE `user_permission_cache` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `business_account_id` bigint NOT NULL COMMENT '商户账户ID',
  `permission_codes` text NOT NULL COMMENT '权限编码列表，JSON格式',
  `menu_ids` text DEFAULT NULL COMMENT '菜单ID列表，JSON格式',
  `data_permission_rules` text DEFAULT NULL COMMENT '数据权限规则，JSON格式',
  `cache_version` varchar(32) NOT NULL COMMENT '缓存版本号',
  `expire_time` datetime NOT NULL COMMENT '过期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_business_account_id` (`business_account_id`),
  KEY `idx_expire_time` (`expire_time`),
  KEY `idx_cache_version` (`cache_version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户权限缓存表';

-- =============================================
-- 7. 权限操作日志表
-- =============================================

-- 权限操作日志表
CREATE TABLE `permission_operation_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `operation_type` varchar(20) NOT NULL COMMENT '操作类型：GRANT-授权，REVOKE-撤销，LOGIN-登录，ACCESS-访问',
  `operator_id` bigint NOT NULL COMMENT '操作人ID',
  `target_user_id` bigint DEFAULT NULL COMMENT '目标用户ID',
  `target_role_id` bigint DEFAULT NULL COMMENT '目标角色ID',
  `permission_code` varchar(100) DEFAULT NULL COMMENT '权限编码',
  `resource_path` varchar(200) DEFAULT NULL COMMENT '访问资源路径',
  `operation_result` varchar(20) NOT NULL COMMENT '操作结果：SUCCESS-成功，FAILURE-失败',
  `failure_reason` varchar(500) DEFAULT NULL COMMENT '失败原因',
  `client_ip` varchar(50) DEFAULT NULL COMMENT '客户端IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `operation_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_operator_id` (`operator_id`),
  KEY `idx_target_user_id` (`target_user_id`),
  KEY `idx_target_role_id` (`target_role_id`),
  KEY `idx_operation_time` (`operation_time`),
  KEY `idx_operation_result` (`operation_result`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限操作日志表';
